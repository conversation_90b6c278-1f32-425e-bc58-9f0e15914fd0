// Comprehensive tests for the enhanced configuration system
// Tests validation, environment variables, secrets, and error handling

use basilisk_bot::config::Config;
use std::env;
use tempfile::TempDir;
use std::fs;

#[cfg(test)]
mod config_system_tests {
    use super::*;

    fn setup_test_env() {
        // Clear any existing environment variables that might interfere
        env::remove_var("APP_STRATEGY__KELLY_FRACTION_CAP");
        env::remove_var("APP_EXECUTION__MAX_SLIPPAGE_BPS");
        env::remove_var("APP_SECRETS__PRIVATE_KEYS__EXECUTION");
        env::remove_var("APP_ENV");
        env::remove_var("CONFIG_PATH");
    }

    fn create_test_config_file(content: &str) -> TempDir {
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let config_path = temp_dir.path().join("test.toml");
        fs::write(&config_path, content).expect("Failed to write test config");
        
        // Set CONFIG_PATH to our test file
        env::set_var("CONFIG_PATH", config_path.to_str().unwrap());
        temp_dir
    }

    #[test]
    fn test_environment_variable_overrides() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set environment variable overrides
        env::set_var("APP_STRATEGY__KELLY_FRACTION_CAP", "0.15");
        env::set_var("APP_EXECUTION__MAX_SLIPPAGE_BPS", "300");
        
        let config = Config::load().expect("Failed to load config");
        
        // Verify overrides took effect
        assert_eq!(config.strategy.kelly_fraction_cap, 0.15);
        assert_eq!(config.execution.max_slippage_bps, 300);
        
        // Verify non-overridden values remain
        assert_eq!(config.app_name, "test_bot");
        assert_eq!(config.strategy.min_profitability_bps, 50);
    }

    #[test]
    fn test_secrets_validation_production() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set production environment
        env::set_var("APP_ENV", "production");
        
        let config = Config::load().expect("Failed to load config");
        
        // Should fail validation due to missing private keys in production
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("No private keys configured"));
    }

    #[test]
    fn test_secrets_validation_with_valid_private_key() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set production environment and valid private key
        env::set_var("APP_ENV", "production");
        env::set_var("APP_SECRETS__PRIVATE_KEYS__EXECUTION", "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef");
        
        let config = Config::load().expect("Failed to load config");
        
        // Should pass validation with valid private key
        let result = config.validate();
        assert!(result.is_ok(), "Validation failed: {:?}", result.err());
        
        // Verify private key was loaded
        assert!(config.secrets.private_keys.contains_key("execution"));
    }

    #[test]
    fn test_invalid_private_key_format() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set production environment and invalid private key
        env::set_var("APP_ENV", "production");
        env::set_var("APP_SECRETS__PRIVATE_KEYS__EXECUTION", "invalid_key");
        
        let config = Config::load().expect("Failed to load config");
        
        // Should fail validation due to invalid private key format
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("invalid format"));
    }

    #[test]
    fn test_kelly_fraction_validation() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 1.5
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        let config = Config::load().expect("Failed to load config");
        
        // Should fail validation due to Kelly fraction > 1.0
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("kelly_fraction_cap must be between 0.0 and 1.0"));
    }

    #[test]
    fn test_production_kelly_fraction_limits() {
        setup_test_env();
        
        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.75
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set production environment and valid private key
        env::set_var("APP_ENV", "production");
        env::set_var("APP_SECRETS__PRIVATE_KEYS__EXECUTION", "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef");
        
        let config = Config::load().expect("Failed to load config");
        
        // Should fail validation due to Kelly fraction > 0.5 in production
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("too aggressive for production"));
    }

    #[test]
    fn test_invalid_rpc_url_validation() {
        setup_test_env();

        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"

[chains.8453]
name = "Base"
rpc_url = "invalid-url"
max_gas_price = 50000000000
private_key_env_var = "BASE_PRIVATE_KEY"

[chains.8453.contracts]

[chains.8453.dex]
"#);

        let config = Config::load().expect("Failed to load config");

        // Should fail validation due to invalid RPC URL
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not a valid URL"));
    }

    #[test]
    fn test_backward_compatibility_private_key() {
        setup_test_env();

        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 200
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set legacy environment variable
        env::set_var("BASILISK_EXECUTION_PRIVATE_KEY", "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef");

        let config = Config::load().expect("Failed to load config");

        // The operational_modes.rs should be able to find the private key via backward compatibility
        // This tests the fallback mechanism we implemented
        assert!(env::var("BASILISK_EXECUTION_PRIVATE_KEY").is_ok());
    }

    #[test]
    fn test_slippage_validation_limits() {
        setup_test_env();

        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 1500
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        let config = Config::load().expect("Failed to load config");

        // Should fail validation due to slippage > 1000 bps (10%)
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("cannot exceed 1000"));
    }

    #[test]
    fn test_production_slippage_limits() {
        setup_test_env();

        let _temp_dir = create_test_config_file(r#"
app_name = "test_bot"
log_level = "info"

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["gaze"]

[execution]
max_slippage_bps = 400
max_gas_price_gwei = 50

[secrets]

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5

[nats]
url = "nats://localhost:4222"
"#);

        // Set production environment and valid private key
        env::set_var("APP_ENV", "production");
        env::set_var("APP_SECRETS__PRIVATE_KEYS__EXECUTION", "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef");

        let config = Config::load().expect("Failed to load config");

        // Should fail validation due to slippage > 300 bps in production
        let result = config.validate();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("too high for production"));
    }
}
