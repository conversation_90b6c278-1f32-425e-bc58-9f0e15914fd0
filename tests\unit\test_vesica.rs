// Unit Tests for Vesica Piscis and Mandorla Gauge
// Tests sacred geometry calculations and AMM arbitrage depth analysis

use basilisk_bot::math::vesica::{
    calculate_mandorla_depth, calculate_amount_to_equalize, optimized_sqrt
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use proptest::prelude::*;
use pretty_assertions::assert_eq;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_optimized_sqrt_basic() {
        let test_cases = vec![
            (dec!(0), dec!(0)),
            (dec!(1), dec!(1)),
            (dec!(4), dec!(2)),
            (dec!(9), dec!(3)),
            (dec!(16), dec!(4)),
            (dec!(25), dec!(5)),
            (dec!(100), dec!(10)),
        ];

        for (input, expected) in test_cases {
            let result = optimized_sqrt(input);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(0.00001),
                "optimized_sqrt({}) failed. Result: {}, Expected: {}, Diff: {}",
                input, result, expected, difference
            );
        }
    }

    #[test]
    fn test_optimized_sqrt_precision() {
        // Test high-precision cases
        let sqrt_2 = optimized_sqrt(dec!(2));
        let expected_sqrt_2 = dec!(1.41421356237);
        let difference = (sqrt_2 - expected_sqrt_2).abs();
        assert!(difference < dec!(0.00001), "sqrt(2) precision test failed");

        let sqrt_3 = optimized_sqrt(dec!(3));
        let expected_sqrt_3 = dec!(1.73205080757);
        let difference = (sqrt_3 - expected_sqrt_3).abs();
        assert!(difference < dec!(0.00001), "sqrt(3) precision test failed");

        let sqrt_5 = optimized_sqrt(dec!(5));
        let expected_sqrt_5 = dec!(2.23606797750);
        let difference = (sqrt_5 - expected_sqrt_5).abs();
        assert!(difference < dec!(0.00001), "sqrt(5) precision test failed");
    }

    #[test]
    fn test_optimized_sqrt_edge_cases() {
        // Test very small numbers
        let small_result = optimized_sqrt(dec!(0.000001));
        assert!(small_result > dec!(0));
        assert!(small_result < dec!(0.01));

        // Test very large numbers
        let large_result = optimized_sqrt(dec!(1000000));
        assert_eq!(large_result, dec!(1000));

        // Test decimal precision
        let precise_result = optimized_sqrt(dec!(1.44));
        let expected = dec!(1.2);
        let difference = (precise_result - expected).abs();
        assert!(difference < dec!(0.00001));
    }

    #[test]
    fn test_mandorla_depth_basic() {
        // Test case from the documentation
        let depth = calculate_mandorla_depth(
            dec!(50000),
            dec!(30000),
            dec!(0.02), // 2% price deviation
        );
        
        // Expected: 50000 * (sqrt(1.02) - 1) ≈ 497.52
        let expected = dec!(497.52469);
        let difference = (depth - expected).abs();
        assert!(
            difference < dec!(0.01),
            "Mandorla depth calculation failed. Result: {}, Expected: {}, Diff: {}",
            depth, expected, difference
        );
    }

    #[test]
    fn test_mandorla_depth_various_scenarios() {
        let test_cases = vec![
            // (pool_a_reserves, pool_b_reserves, price_deviation, expected_depth)
            (dec!(100000), dec!(80000), dec!(0.05), dec!(2439.26)), // 5% deviation
            (dec!(10000), dec!(10000), dec!(0.01), dec!(49.88)), // 1% deviation, equal pools
            (dec!(1000), dec!(2000), dec!(0.03), dec!(74.54)), // 3% deviation, different sizes
        ];

        for (pool_a, pool_b, deviation, expected) in test_cases {
            let result = calculate_mandorla_depth(pool_a, pool_b, deviation);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(1.0),
                "Mandorla depth test failed for pools ({}, {}), deviation {}: got {}, expected {}",
                pool_a, pool_b, deviation, result, expected
            );
        }
    }

    #[test]
    fn test_amount_to_equalize_positive_deviation() {
        // When Pool B is more expensive than Pool A (positive deviation)
        let result = calculate_amount_to_equalize(
            dec!(50000), // Pool A reserves
            dec!(30000), // Pool B reserves  
            dec!(0.02),  // 2% price deviation
        );
        
        // Should return positive amount
        assert!(result > dec!(0));
        assert!(result < dec!(1000)); // Reasonable upper bound
    }

    #[test]
    fn test_amount_to_equalize_zero_deviation() {
        // When pools have equal prices
        let result = calculate_amount_to_equalize(
            dec!(50000),
            dec!(30000),
            dec!(0), // No price deviation
        );
        
        // Should return zero (no arbitrage opportunity)
        assert_eq!(result, dec!(0));
    }

    #[test]
    fn test_amount_to_equalize_edge_cases() {
        // Test with very small reserves
        let small_result = calculate_amount_to_equalize(
            dec!(1),
            dec!(1),
            dec!(0.01),
        );
        assert!(small_result >= dec!(0));
        assert!(small_result < dec!(1));

        // Test with very large reserves
        let large_result = calculate_amount_to_equalize(
            dec!(1000000),
            dec!(1000000),
            dec!(0.001), // 0.1% deviation
        );
        assert!(large_result >= dec!(0));
        assert!(large_result > dec!(0)); // Should be positive for any deviation
    }

    #[test]
    fn test_vesica_mathematical_properties() {
        // Test that mandorla depth is proportional to pool size
        let base_depth = calculate_mandorla_depth(dec!(1000), dec!(1000), dec!(0.02));
        let scaled_depth = calculate_mandorla_depth(dec!(2000), dec!(2000), dec!(0.02));
        
        // Scaled depth should be approximately 2x base depth
        let ratio = scaled_depth / base_depth;
        let difference = (ratio - dec!(2)).abs();
        assert!(difference < dec!(0.01), "Depth scaling property failed");

        // Test that depth increases with price deviation
        let small_dev_depth = calculate_mandorla_depth(dec!(1000), dec!(1000), dec!(0.01));
        let large_dev_depth = calculate_mandorla_depth(dec!(1000), dec!(1000), dec!(0.05));
        
        assert!(large_dev_depth > small_dev_depth, "Depth should increase with deviation");
    }

    #[test]
    fn test_vesica_symmetry() {
        // Test that swapping pool A and B gives same result for same deviation
        let depth_ab = calculate_mandorla_depth(dec!(1000), dec!(2000), dec!(0.02));
        let depth_ba = calculate_mandorla_depth(dec!(2000), dec!(1000), dec!(0.02));
        
        // Results should be similar (within tolerance due to which pool is "cheaper")
        let difference = (depth_ab - depth_ba).abs();
        assert!(difference < dec!(50), "Vesica calculation should be roughly symmetric");
    }
}

// Property-based tests for Vesica Piscis
proptest! {
    #[test]
    fn test_optimized_sqrt_properties(
        x in 0.0..1000000.0_f64
    ) {
        let decimal_x = Decimal::from_f64(x).unwrap();
        let sqrt_result = optimized_sqrt(decimal_x);
        
        // sqrt should always be non-negative
        prop_assert!(sqrt_result >= Decimal::ZERO);
        
        // sqrt(0) should be 0
        if decimal_x == Decimal::ZERO {
            prop_assert_eq!(sqrt_result, Decimal::ZERO);
        }
        
        // sqrt(1) should be 1
        if (decimal_x - Decimal::ONE).abs() < dec!(0.0001) {
            prop_assert!((sqrt_result - Decimal::ONE).abs() < dec!(0.0001));
        }
        
        // sqrt(x)^2 should approximately equal x
        let squared = sqrt_result * sqrt_result;
        let difference = (squared - decimal_x).abs();
        prop_assert!(difference < dec!(0.01));
    }

    #[test]
    fn test_mandorla_depth_properties(
        pool_a_reserves in 1.0..1000000.0_f64,
        pool_b_reserves in 1.0..1000000.0_f64,
        price_deviation in 0.0..0.1_f64 // 0% to 10% deviation
    ) {
        let pool_a = Decimal::from_f64(pool_a_reserves).unwrap();
        let pool_b = Decimal::from_f64(pool_b_reserves).unwrap();
        let deviation = Decimal::from_f64(price_deviation).unwrap();
        
        let depth = calculate_mandorla_depth(pool_a, pool_b, deviation);
        
        // Depth should always be non-negative
        prop_assert!(depth >= Decimal::ZERO);
        
        // Depth should be zero when deviation is zero
        if deviation == Decimal::ZERO {
            prop_assert_eq!(depth, Decimal::ZERO);
        }
        
        // Depth should be positive when deviation is positive
        if deviation > Decimal::ZERO {
            prop_assert!(depth >= Decimal::ZERO);
        }
        
        // Depth should be bounded by the smaller pool's reserves
        let min_reserves = pool_a.min(pool_b);
        prop_assert!(depth <= min_reserves);
    }

    #[test]
    fn test_amount_to_equalize_properties(
        pool_a_reserves in 1.0..1000000.0_f64,
        pool_b_reserves in 1.0..1000000.0_f64,
        price_deviation in -0.1..0.1_f64 // -10% to +10% deviation
    ) {
        let pool_a = Decimal::from_f64(pool_a_reserves).unwrap();
        let pool_b = Decimal::from_f64(pool_b_reserves).unwrap();
        let deviation = Decimal::from_f64(price_deviation).unwrap();
        
        let amount = calculate_amount_to_equalize(pool_a, pool_b, deviation);
        
        // Amount should always be non-negative
        prop_assert!(amount >= Decimal::ZERO);
        
        // Amount should be zero when deviation is zero
        if deviation.abs() < dec!(0.0001) {
            prop_assert!(amount < dec!(0.01)); // Allow small numerical errors
        }
    }
}
