// src/validation/tests/are_validator_tests.rs

//! Comprehensive tests for the Aetheric Resonance Engine Validator
//! 
//! This module contains tests that validate the ARE validator implementation,
//! ensuring it correctly tests all three pillars and their integration.

use crate::validation::{
    AREValidator, AREValidationMetrics, ValidationConfig, ValidationStatus,
    ValidationFrameworkResult
};
use crate::shared_types::{
    GeometricScore, TemporalHarmonics, NetworkResonanceState, MarketCharacter,
    MarketRegime
};
use crate::shared_types::are_analysis::AREDecision;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::time::Duration;
use tokio;

/// Test the creation and basic functionality of AREValidator
#[tokio::test]
async fn test_are_validator_creation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    // Verify validator was created successfully
    // Note: Fields are private, so we just verify creation succeeded
}

/// Test Chronos Sieve temporal analysis validation
#[tokio::test]
async fn test_chronos_sieve_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_chronos_sieve().await;
    assert!(result.is_ok(), "Chronos Sieve validation should succeed");
    
    let metrics = result.unwrap();
    
    // Verify metrics structure
    assert!(metrics.scenarios_tested > 0, "Should test at least one scenario");
    assert!(metrics.fft_verification_accuracy >= 0.0 && metrics.fft_verification_accuracy <= 1.0);
    assert!(metrics.temporal_harmonics_accuracy >= 0.0 && metrics.temporal_harmonics_accuracy <= 1.0);
    assert!(metrics.fractal_analysis_accuracy >= 0.0 && metrics.fractal_analysis_accuracy <= 1.0);
    assert!(metrics.market_regime_classification_accuracy >= 0.0 && metrics.market_regime_classification_accuracy <= 1.0);
    // Calculation time should be recorded (always non-negative for u64)
    assert!(metrics.calculation_time_ms < 10000, "Should complete within reasonable time");
    
    // Verify reasonable accuracy levels
    assert!(metrics.fft_verification_accuracy > 0.8, "FFT verification should be highly accurate");
    assert!(metrics.temporal_harmonics_accuracy > 0.8, "Temporal harmonics should be accurate");
}

/// Test Mandorla Gauge geometric analysis validation
#[tokio::test]
async fn test_mandorla_gauge_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_mandorla_gauge().await;
    assert!(result.is_ok(), "Mandorla Gauge validation should succeed");
    
    let metrics = result.unwrap();
    
    // Verify metrics structure
    assert!(metrics.scenarios_tested > 0);
    assert!(metrics.vesica_piscis_accuracy >= 0.0 && metrics.vesica_piscis_accuracy <= 1.0);
    assert!(metrics.convexity_ratio_accuracy >= 0.0 && metrics.convexity_ratio_accuracy <= 1.0);
    assert!(metrics.liquidity_centroid_accuracy >= 0.0 && metrics.liquidity_centroid_accuracy <= 1.0);
    assert!(metrics.harmonic_path_accuracy >= 0.0 && metrics.harmonic_path_accuracy <= 1.0);
    assert!(metrics.geometric_consistency >= 0.0 && metrics.geometric_consistency <= 1.0);
    assert!(metrics.calculation_time_ms < 10000, "Should complete within reasonable time");
    
    // Verify reasonable accuracy levels
    assert!(metrics.vesica_piscis_accuracy > 0.8, "Vesica Piscis calculation should be accurate");
    assert!(metrics.geometric_consistency > 0.7, "Geometric components should be consistent");
}

/// Test Network Seismology validation
#[tokio::test]
async fn test_network_seismology_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_network_seismology().await;
    assert!(result.is_ok(), "Network Seismology validation should succeed");
    
    let metrics = result.unwrap();
    
    // Verify metrics structure
    assert!(metrics.scenarios_tested > 0);
    assert!(metrics.latency_measurement_accuracy >= 0.0 && metrics.latency_measurement_accuracy <= 1.0);
    assert!(metrics.coherence_testing_accuracy >= 0.0 && metrics.coherence_testing_accuracy <= 1.0);
    assert!(metrics.block_propagation_accuracy >= 0.0 && metrics.block_propagation_accuracy <= 1.0);
    assert!(metrics.network_health_accuracy >= 0.0 && metrics.network_health_accuracy <= 1.0);
    assert!(metrics.timing_precision >= 0.0 && metrics.timing_precision <= 1.0);
    assert!(metrics.calculation_time_ms < 10000, "Should complete within reasonable time");
    
    // Verify high precision requirements for network timing
    assert!(metrics.timing_precision > 0.9, "Network timing should be highly precise");
    assert!(metrics.latency_measurement_accuracy > 0.9, "Latency measurement should be accurate");
}

/// Test multiplicative scoring model validation with zero-veto behavior
#[tokio::test]
async fn test_multiplicative_scoring_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_multiplicative_scoring().await;
    assert!(result.is_ok(), "Multiplicative scoring validation should succeed");
    
    let metrics = result.unwrap();
    
    // Verify metrics structure
    assert!(metrics.scenarios_tested > 0);
    assert!(metrics.zero_veto_behavior_accuracy >= 0.0 && metrics.zero_veto_behavior_accuracy <= 1.0);
    assert!(metrics.score_multiplication_accuracy >= 0.0 && metrics.score_multiplication_accuracy <= 1.0);
    assert!(metrics.threshold_enforcement_accuracy >= 0.0 && metrics.threshold_enforcement_accuracy <= 1.0);
    assert!(metrics.edge_case_handling >= 0.0 && metrics.edge_case_handling <= 1.0);
    assert!(metrics.calculation_time_ms < 10000, "Should complete within reasonable time");
    
    // Critical requirement: zero-veto behavior must be perfect
    assert_eq!(metrics.zero_veto_behavior_accuracy, 1.0, "Zero-veto behavior must be 100% accurate");
    assert_eq!(metrics.threshold_enforcement_accuracy, 1.0, "Threshold enforcement must be 100% accurate");
    
    // Other components should be highly accurate
    assert!(metrics.score_multiplication_accuracy > 0.95, "Score multiplication should be highly accurate");
    assert!(metrics.edge_case_handling > 0.9, "Edge case handling should be robust");
}

/// Test pillar integration validation
#[tokio::test]
async fn test_pillar_integration_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_pillar_integration().await;
    assert!(result.is_ok(), "Pillar integration validation should succeed");
    
    let metrics = result.unwrap();
    
    // Verify metrics structure
    assert!(metrics.scenarios_tested > 0);
    assert!(metrics.weight_application_accuracy >= 0.0 && metrics.weight_application_accuracy <= 1.0);
    assert!(metrics.pillar_coordination_accuracy >= 0.0 && metrics.pillar_coordination_accuracy <= 1.0);
    assert!(metrics.final_score_calculation_accuracy >= 0.0 && metrics.final_score_calculation_accuracy <= 1.0);
    assert!(metrics.integration_consistency >= 0.0 && metrics.integration_consistency <= 1.0);
    assert!(metrics.calculation_time_ms < 10000, "Should complete within reasonable time");
    
    // Integration components should be highly accurate
    assert!(metrics.weight_application_accuracy > 0.95, "Weight application should be highly accurate");
    assert!(metrics.final_score_calculation_accuracy > 0.95, "Final score calculation should be highly accurate");
    assert!(metrics.pillar_coordination_accuracy > 0.9, "Pillar coordination should be accurate");
    assert!(metrics.integration_consistency > 0.9, "Integration should be consistent");
}

/// Test complete ARE validation with all pillars
#[tokio::test]
async fn test_complete_are_validation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_aetheric_resonance_engine().await;
    assert!(result.is_ok(), "Complete ARE validation should succeed");
    
    let validation_result = result.unwrap();
    
    // Verify validation result structure
    assert_eq!(validation_result.test_name, "Comprehensive Aetheric Resonance Engine Validation");
    assert!(validation_result.status != ValidationStatus::Failed, "ARE validation should not fail");
    assert!(validation_result.execution_time > Duration::from_millis(0));
    
    // Verify comprehensive metrics
    let metrics = validation_result.metrics;
    assert!(metrics.overall_accuracy_score > 0.0 && metrics.overall_accuracy_score <= 1.0);
    assert!(metrics.total_validation_time > Duration::from_millis(0));
    
    // Verify all pillar metrics are present
    assert!(metrics.chronos_sieve_metrics.scenarios_tested > 0);
    assert!(metrics.mandorla_gauge_metrics.scenarios_tested > 0);
    assert!(metrics.network_seismology_metrics.scenarios_tested > 0);
    assert!(metrics.multiplicative_scoring_metrics.scenarios_tested > 0);
    assert!(metrics.pillar_integration_metrics.scenarios_tested > 0);
    
    // Verify overall accuracy is reasonable
    assert!(metrics.overall_accuracy_score > 0.85, "Overall ARE accuracy should be high");
    
    // If accuracy is high enough, status should be Passed
    if metrics.overall_accuracy_score >= 0.95 {
        assert_eq!(validation_result.status, ValidationStatus::Passed);
    } else if metrics.overall_accuracy_score >= 0.90 {
        assert_eq!(validation_result.status, ValidationStatus::Warning);
    }
}

/// Test ARE validator with edge cases and error conditions
#[tokio::test]
async fn test_are_validator_edge_cases() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    // Test that validator handles edge cases gracefully
    let result = validator.validate_aetheric_resonance_engine().await;
    assert!(result.is_ok(), "ARE validator should handle edge cases gracefully");
    
    let validation_result = result.unwrap();
    
    // Even with edge cases, should not crash and should provide meaningful results
    assert!(validation_result.metrics.overall_accuracy_score >= 0.0);
    assert!(validation_result.execution_time > Duration::from_millis(0));
    
    // Should have reasonable number of scenarios tested
    let total_scenarios = validation_result.metrics.chronos_sieve_metrics.scenarios_tested +
                         validation_result.metrics.mandorla_gauge_metrics.scenarios_tested +
                         validation_result.metrics.network_seismology_metrics.scenarios_tested +
                         validation_result.metrics.multiplicative_scoring_metrics.scenarios_tested +
                         validation_result.metrics.pillar_integration_metrics.scenarios_tested;
    
    assert!(total_scenarios > 0, "Should test at least some scenarios");
}

/// Test mock geometric scorer functionality
#[tokio::test]
async fn test_mock_geometric_scorer() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    // Note: mock_geometric_scorer is private, so we test through the public interface
    // The validator creation itself tests that the mock scorer is properly initialized
}

/// Test ARE test data generator
#[tokio::test]
async fn test_are_test_data_generator() {
    let generator = crate::validation::ARETestDataGenerator::new(42);
    
    // Test Chronos Sieve scenario generation
    let chronos_scenarios = generator.generate_chronos_test_scenarios();
    assert!(!chronos_scenarios.is_empty(), "Should generate Chronos test scenarios");
    
    for scenario in &chronos_scenarios {
        assert!(!scenario.name.is_empty(), "Scenario should have a name");
        assert!(!scenario.price_data.is_empty(), "Scenario should have price data");
        assert!(!scenario.expected_harmonics.dominant_cycles_minutes.is_empty(), "Should have valid harmonics");
    }
    
    // Test Mandorla Gauge scenario generation
    let mandorla_scenarios = generator.generate_mandorla_test_scenarios();
    assert!(!mandorla_scenarios.is_empty(), "Should generate Mandorla test scenarios");
    
    for scenario in &mandorla_scenarios {
        assert!(!scenario.name.is_empty(), "Scenario should have a name");
        assert!(!scenario.arbitrage_path.is_empty(), "Scenario should have arbitrage path");
        assert!(scenario.expected_vesica_depth >= dec!(0.0), "Should have valid vesica depth");
    }
    
    // Test Network Seismology scenario generation
    let seismology_scenarios = generator.generate_seismology_test_scenarios();
    assert!(!seismology_scenarios.is_empty(), "Should generate Seismology test scenarios");
    
    for scenario in &seismology_scenarios {
        assert!(!scenario.name.is_empty(), "Scenario should have a name");
        assert!(!scenario.network_data.node_latencies.is_empty(), "Should have network data");
        assert!(scenario.expected_latency_ms > 0, "Should have valid expected latency");
    }
    
    // Test Multiplicative Scoring scenario generation
    let multiplicative_scenarios = generator.generate_multiplicative_scoring_scenarios();
    assert!(!multiplicative_scenarios.is_empty(), "Should generate Multiplicative scoring scenarios");
    
    for scenario in &multiplicative_scenarios {
        assert!(!scenario.name.is_empty(), "Scenario should have a name");
        assert!(scenario.temporal_score >= dec!(0.0) && scenario.temporal_score <= dec!(1.0));
        assert!(scenario.geometric_score >= dec!(0.0) && scenario.geometric_score <= dec!(1.0));
        assert!(scenario.network_score >= dec!(0.0) && scenario.network_score <= dec!(1.0));
    }
    
    // Test Integration scenario generation
    let integration_scenarios = generator.generate_integration_test_scenarios();
    assert!(!integration_scenarios.is_empty(), "Should generate Integration test scenarios");
    
    for scenario in &integration_scenarios {
        assert!(!scenario.name.is_empty(), "Scenario should have a name");
        assert!(scenario.temporal_weight + scenario.geometric_weight + scenario.network_weight > dec!(0.9));
        assert!(scenario.temporal_weight + scenario.geometric_weight + scenario.network_weight < dec!(1.1));
    }
}

/// Test validation accuracy calculation
#[tokio::test]
async fn test_validation_accuracy_calculation() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_aetheric_resonance_engine().await;
    assert!(result.is_ok());
    
    let validation_result = result.unwrap();
    let metrics = validation_result.metrics;
    
    // Verify that overall accuracy is a reasonable weighted average of component accuracies
    let chronos_score = (metrics.chronos_sieve_metrics.fft_verification_accuracy + 
                        metrics.chronos_sieve_metrics.temporal_harmonics_accuracy + 
                        metrics.chronos_sieve_metrics.fractal_analysis_accuracy + 
                        metrics.chronos_sieve_metrics.market_regime_classification_accuracy) / 4.0;
    
    let mandorla_score = (metrics.mandorla_gauge_metrics.vesica_piscis_accuracy + 
                         metrics.mandorla_gauge_metrics.convexity_ratio_accuracy + 
                         metrics.mandorla_gauge_metrics.liquidity_centroid_accuracy + 
                         metrics.mandorla_gauge_metrics.harmonic_path_accuracy + 
                         metrics.mandorla_gauge_metrics.geometric_consistency) / 5.0;
    
    // Overall accuracy should be influenced by all components
    assert!(metrics.overall_accuracy_score > 0.0);
    assert!(metrics.overall_accuracy_score <= 1.0);
    
    // Should be reasonably close to component averages (within reasonable bounds)
    let min_expected = (chronos_score + mandorla_score) / 4.0; // Conservative estimate
    let max_expected = 1.0; // Maximum possible
    
    assert!(metrics.overall_accuracy_score >= min_expected);
    assert!(metrics.overall_accuracy_score <= max_expected);
}

/// Test validation status determination
#[tokio::test]
async fn test_validation_status_determination() {
    let config = ValidationConfig::default();
    let validator = AREValidator::new(config);
    
    let result = validator.validate_aetheric_resonance_engine().await;
    assert!(result.is_ok());
    
    let validation_result = result.unwrap();
    
    // Status should be determined based on overall accuracy
    match validation_result.metrics.overall_accuracy_score {
        score if score >= 0.95 => {
            assert_eq!(validation_result.status, ValidationStatus::Passed);
            assert!(validation_result.errors.is_empty(), "High accuracy should have no errors");
        },
        score if score >= 0.90 => {
            assert_eq!(validation_result.status, ValidationStatus::Warning);
            assert!(validation_result.warnings.len() > 0, "Medium accuracy should have warnings");
        },
        _ => {
            assert_eq!(validation_result.status, ValidationStatus::Failed);
            assert!(validation_result.errors.len() > 0, "Low accuracy should have errors");
        }
    }
}

/// Test that validation produces consistent results across multiple runs
#[tokio::test]
async fn test_validation_consistency() {
    let config = ValidationConfig::default();
    
    // Run validation multiple times
    let mut results = Vec::new();
    for _ in 0..3 {
        let validator = AREValidator::new(config.clone());
        let result = validator.validate_aetheric_resonance_engine().await;
        assert!(result.is_ok());
        results.push(result.unwrap());
    }
    
    // Results should be consistent (same seed should produce same results)
    let first_accuracy = results[0].metrics.overall_accuracy_score;
    for result in &results[1..] {
        let accuracy_diff = (result.metrics.overall_accuracy_score - first_accuracy).abs();
        assert!(accuracy_diff < 0.01, "Results should be consistent across runs");
    }
    
    // All should have same status
    let first_status = &results[0].status;
    for result in &results[1..] {
        assert_eq!(&result.status, first_status, "Status should be consistent across runs");
    }
}