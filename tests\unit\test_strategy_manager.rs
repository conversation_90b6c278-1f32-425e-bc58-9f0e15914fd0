// Unit Tests for Strategy Manager
// Tests strategy coordination, opportunity processing, and system integration

use basilisk_bot::strategies::manager::StrategyManager;
use basilisk_bot::strategies::centrality_manager::{CentralityScoreManager, CentralityStatistics};
use basilisk_bot::shared_types::*;
use basilisk_bot::config::Config;
use basilisk_bot::math::scoring::DummyGeometricScorer;
use basilisk_bot::execution::gas_estimator::GasEstimator;
use basilisk_bot::data::price_oracle::PriceOracle;
use ethers::types::Address;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tokio::sync::{mpsc, Mutex};
use mockall::predicate::*;
use mockall::mock;
use proptest::prelude::*;
use pretty_assertions::assert_eq;
use std::sync::Arc;
use std::collections::HashMap;
use tokio_test;

// Mock NATS client for testing
mock! {
    NatsClient {}
    
    impl Clone for NatsClient {
        fn clone(&self) -> Self;
    }
    
    // Add necessary NATS client methods here
    async fn publish(&self, subject: &str, payload: Vec<u8>) -> Result<(), async_nats::Error>;
    async fn subscribe(&self, subject: &str) -> Result<async_nats::Subscriber, async_nats::Error>;
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> Arc<Config> {
        // Create a minimal test configuration
        Arc::new(Config::default())
    }

    fn create_test_opportunity() -> Opportunity {
        Opportunity::DexArbitrage {
            base: OpportunityBase {
                id: "test_opp_1".to_string(),
                source_scanner: "test_scanner".to_string(),
                estimated_gross_profit_usd: dec!(100.0),
                associated_volatility: dec!(0.1),
                requires_flash_liquidity: false,
                chain_id: 1,
                timestamp: 1640995200,
                intersection_value_usd: dec!(50.0),
                aetheric_resonance_score: None,
            },
            data: DexArbitrageData {
                path: vec![
                    ArbitragePool {
                        address: Address::random(),
                        token0_symbol: "WETH".to_string(),
                        token1_symbol: "USDC".to_string(),
                        reserve0: dec!(1000.0),
                        reserve1: dec!(2000000.0),
                        protocol: "uniswap".to_string(),
                    },
                ],
                amount_in: dec!(10.0),
                expected_amount_out: dec!(10.05),
            },
        }
    }

    #[test]
    fn test_centrality_score_manager_creation() {
        let manager = CentralityScoreManager::new();
        
        // Should have default scores for major tokens
        assert!(manager.get_centrality_score("WETH") > dec!(0.0));
        assert!(manager.get_centrality_score("USDC") > dec!(0.0));
        assert!(manager.get_centrality_score("USDT") > dec!(0.0));
        
        // Unknown tokens should get default score
        let unknown_score = manager.get_centrality_score("UNKNOWN_TOKEN");
        assert_eq!(unknown_score, dec!(0.3)); // Default fallback
    }

    #[test]
    fn test_centrality_score_manager_update() {
        let mut manager = CentralityScoreManager::new();
        
        // Update a score
        let result = manager.update_score("TEST_TOKEN".to_string(), dec!(0.75));
        assert!(result.is_ok());
        
        // Verify the update
        assert_eq!(manager.get_centrality_score("TEST_TOKEN"), dec!(0.75));
        
        // Test invalid score bounds
        let invalid_result = manager.update_score("INVALID".to_string(), dec!(1.5));
        assert!(invalid_result.is_err());
        
        let negative_result = manager.update_score("NEGATIVE".to_string(), dec!(-0.1));
        assert!(negative_result.is_err());
    }

    #[test]
    fn test_centrality_statistics() {
        let manager = CentralityScoreManager::new();
        let stats = manager.get_statistics();
        
        assert!(stats.total_tokens > 0);
        assert!(stats.average_score >= dec!(0.0));
        assert!(stats.average_score <= dec!(1.0));
        assert!(stats.highest_score >= stats.lowest_score);
        assert!(stats.highest_score <= dec!(1.0));
        assert!(stats.lowest_score >= dec!(0.0));
    }

    #[test]
    fn test_centrality_score_bounds() {
        let manager = CentralityScoreManager::new();
        let all_scores = manager.get_all_scores();
        
        // All scores should be between 0 and 1
        for (token, score) in all_scores.iter() {
            assert!(
                *score >= dec!(0.0) && *score <= dec!(1.0),
                "Token {} has invalid score: {}",
                token,
                score
            );
        }
    }

    #[test]
    fn test_centrality_score_ordering() {
        let manager = CentralityScoreManager::new();
        
        // Major tokens should have higher scores than minor ones
        let weth_score = manager.get_centrality_score("WETH");
        let usdc_score = manager.get_centrality_score("USDC");
        let unknown_score = manager.get_centrality_score("UNKNOWN_TOKEN");
        
        assert!(weth_score > unknown_score);
        assert!(usdc_score > unknown_score);
    }

    #[tokio::test]
    async fn test_opportunity_base_validation() {
        let opportunity = create_test_opportunity();
        let base = opportunity.base();
        
        // Verify basic properties
        assert!(!base.id.is_empty());
        assert!(!base.source_scanner.is_empty());
        assert!(base.estimated_gross_profit_usd > dec!(0.0));
        assert!(base.associated_volatility >= dec!(0.0));
        assert!(base.chain_id > 0);
        assert!(base.timestamp > 0);
        assert!(base.intersection_value_usd >= dec!(0.0));
    }

    #[tokio::test]
    async fn test_opportunity_quality_ratio() {
        let opportunity = create_test_opportunity();
        let base = opportunity.base();
        
        let quality_ratio = base.intersection_value_usd / base.estimated_gross_profit_usd;
        
        // Quality ratio should be reasonable (between 0 and 1)
        assert!(quality_ratio >= dec!(0.0));
        assert!(quality_ratio <= dec!(1.0));
    }

    #[tokio::test]
    async fn test_dex_arbitrage_data_validation() {
        let opportunity = create_test_opportunity();
        
        if let Opportunity::DexArbitrage { data, .. } = opportunity {
            assert!(!data.path.is_empty());
            assert!(data.amount_in > dec!(0.0));
            assert!(data.expected_amount_out > dec!(0.0));
            
            // Verify pool data
            for pool in &data.path {
                assert!(!pool.token0_symbol.is_empty());
                assert!(!pool.token1_symbol.is_empty());
                assert!(pool.reserve0 > dec!(0.0));
                assert!(pool.reserve1 > dec!(0.0));
                assert!(!pool.protocol.is_empty());
            }
        } else {
            panic!("Expected DexArbitrage opportunity");
        }
    }

    #[test]
    fn test_market_regime_enum_completeness() {
        // Test all market regime variants
        let regimes = vec![
            MarketRegime::CalmOrderly,
            MarketRegime::RetailFomoSpike,
            MarketRegime::BotGasWar,
            MarketRegime::HighVolatilityCorrection,
            MarketRegime::Trending,
            MarketRegime::Unknown,
        ];
        
        // Each regime should be serializable
        for regime in regimes {
            let serialized = serde_json::to_string(&regime).unwrap();
            let deserialized: MarketRegime = serde_json::from_str(&serialized).unwrap();
            assert_eq!(regime, deserialized);
        }
    }

    #[test]
    fn test_opportunity_type_variants() {
        // Test different opportunity types can be created
        let dex_arb = create_test_opportunity();
        assert!(matches!(dex_arb, Opportunity::DexArbitrage { .. }));
        
        // Test opportunity base access
        let base = dex_arb.base();
        assert!(!base.id.is_empty());
        
        // Test mutable access
        let mut mutable_opp = dex_arb.clone();
        mutable_opp.base_mut().estimated_gross_profit_usd = dec!(200.0);
        assert_eq!(mutable_opp.base().estimated_gross_profit_usd, dec!(200.0));
    }

    #[test]
    fn test_arbitrage_path_type_alias() {
        let pool1 = ArbitragePool {
            address: Address::random(),
            token0_symbol: "WETH".to_string(),
            token1_symbol: "USDC".to_string(),
            reserve0: dec!(1000.0),
            reserve1: dec!(2000000.0),
            protocol: "uniswap".to_string(),
        };
        
        let pool2 = ArbitragePool {
            address: Address::random(),
            token0_symbol: "USDC".to_string(),
            token1_symbol: "DAI".to_string(),
            reserve0: dec!(2000000.0),
            reserve1: dec!(2000000.0),
            protocol: "sushiswap".to_string(),
        };
        
        let path: ArbitragePath = vec![pool1, pool2];
        assert_eq!(path.len(), 2);
        
        // Verify path connectivity
        assert_eq!(path[0].token1_symbol, path[1].token0_symbol);
    }

    #[test]
    fn test_temporal_harmonics_validation() {
        let harmonics = TemporalHarmonics {
            dominant_cycles_minutes: vec![
                (15.0, 0.8),
                (30.0, 0.6),
                (60.0, 0.4),
            ],
            market_rhythm_stability: 0.75,
        };
        
        // Verify cycle data structure
        assert_eq!(harmonics.dominant_cycles_minutes.len(), 3);
        
        // Verify stability is in valid range
        assert!(harmonics.market_rhythm_stability >= 0.0);
        assert!(harmonics.market_rhythm_stability <= 1.0);
        
        // Verify cycles are ordered by period
        for i in 1..harmonics.dominant_cycles_minutes.len() {
            assert!(harmonics.dominant_cycles_minutes[i].0 > harmonics.dominant_cycles_minutes[i-1].0);
        }
    }

    #[test]
    fn test_network_resonance_state_validation() {
        let state = NetworkResonanceState {
            sp_time_ms: 15.5,
            network_coherence_score: 0.85,
            is_shock_event: false,
            sp_time_20th_percentile: 12.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        };
        
        // Verify timing values are reasonable
        assert!(state.sp_time_ms > 0.0);
        assert!(state.sp_time_20th_percentile > 0.0);
        
        // Verify coherence score is in valid range
        assert!(state.network_coherence_score >= 0.0);
        assert!(state.network_coherence_score <= 1.0);
        
        // Verify status is not empty
        assert!(!state.sequencer_status.is_empty());
    }

    #[test]
    fn test_geometric_score_validation() {
        let score = GeometricScore {
            convexity_ratio: dec!(0.75),
            liquidity_centroid_bias: dec!(0.60),
            harmonic_path_score: dec!(0.85),
            vesica_piscis_depth: dec!(0.90),
        };
        
        // All scores should be between 0 and 1
        assert!(score.convexity_ratio >= dec!(0.0) && score.convexity_ratio <= dec!(1.0));
        assert!(score.liquidity_centroid_bias >= dec!(0.0) && score.liquidity_centroid_bias <= dec!(1.0));
        assert!(score.harmonic_path_score >= dec!(0.0) && score.harmonic_path_score <= dec!(1.0));
        assert!(score.vesica_piscis_depth >= dec!(0.0) && score.vesica_piscis_depth <= dec!(1.0));
    }

    #[test]
    fn test_opportunity_serialization() {
        let opportunity = create_test_opportunity();
        
        // Test serialization round-trip
        let serialized = serde_json::to_string(&opportunity).unwrap();
        let deserialized: Opportunity = serde_json::from_str(&serialized).unwrap();
        
        // Verify key fields match
        assert_eq!(opportunity.base().id, deserialized.base().id);
        assert_eq!(opportunity.base().source_scanner, deserialized.base().source_scanner);
        assert_eq!(opportunity.base().estimated_gross_profit_usd, deserialized.base().estimated_gross_profit_usd);
        assert_eq!(opportunity.base().chain_id, deserialized.base().chain_id);
    }
}

// Property-based tests for strategy manager components
proptest! {
    #[test]
    fn test_centrality_score_properties(
        score in 0.0..1.0_f64
    ) {
        let mut manager = CentralityScoreManager::new();
        let decimal_score = Decimal::from_f64(score).unwrap();
        
        let result = manager.update_score("TEST_TOKEN".to_string(), decimal_score);
        prop_assert!(result.is_ok());
        
        let retrieved_score = manager.get_centrality_score("TEST_TOKEN");
        prop_assert_eq!(retrieved_score, decimal_score);
    }

    #[test]
    fn test_opportunity_base_properties(
        profit in 1.0..10000.0_f64,
        volatility in 0.0..1.0_f64,
        intersection_ratio in 0.0..1.0_f64
    ) {
        let profit_decimal = Decimal::from_f64(profit).unwrap();
        let volatility_decimal = Decimal::from_f64(volatility).unwrap();
        let intersection_decimal = profit_decimal * Decimal::from_f64(intersection_ratio).unwrap();
        
        let base = OpportunityBase {
            id: "test_id".to_string(),
            source_scanner: "test_scanner".to_string(),
            estimated_gross_profit_usd: profit_decimal,
            associated_volatility: volatility_decimal,
            requires_flash_liquidity: false,
            chain_id: 1,
            timestamp: 1640995200,
            intersection_value_usd: intersection_decimal,
            aetheric_resonance_score: None,
        };
        
        // Verify properties
        prop_assert!(base.estimated_gross_profit_usd > Decimal::ZERO);
        prop_assert!(base.associated_volatility >= Decimal::ZERO);
        prop_assert!(base.associated_volatility <= Decimal::ONE);
        prop_assert!(base.intersection_value_usd >= Decimal::ZERO);
        prop_assert!(base.intersection_value_usd <= base.estimated_gross_profit_usd);
        
        // Quality ratio should be valid
        let quality_ratio = base.intersection_value_usd / base.estimated_gross_profit_usd;
        prop_assert!(quality_ratio >= Decimal::ZERO);
        prop_assert!(quality_ratio <= Decimal::ONE);
    }

    #[test]
    fn test_geometric_score_properties(
        convexity in 0.0..1.0_f64,
        liquidity in 0.0..1.0_f64,
        harmonic in 0.0..1.0_f64,
        vesica in 0.0..1.0_f64
    ) {
        let score = GeometricScore {
            convexity_ratio: Decimal::from_f64(convexity).unwrap(),
            liquidity_centroid_bias: Decimal::from_f64(liquidity).unwrap(),
            harmonic_path_score: Decimal::from_f64(harmonic).unwrap(),
            vesica_piscis_depth: Decimal::from_f64(vesica).unwrap(),
        };
        
        // All components should be in valid range
        prop_assert!(score.convexity_ratio >= Decimal::ZERO);
        prop_assert!(score.convexity_ratio <= Decimal::ONE);
        prop_assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        prop_assert!(score.liquidity_centroid_bias <= Decimal::ONE);
        prop_assert!(score.harmonic_path_score >= Decimal::ZERO);
        prop_assert!(score.harmonic_path_score <= Decimal::ONE);
        prop_assert!(score.vesica_piscis_depth >= Decimal::ZERO);
        prop_assert!(score.vesica_piscis_depth <= Decimal::ONE);
        
        // Test serialization
        let serialized = serde_json::to_string(&score).unwrap();
        let deserialized: GeometricScore = serde_json::from_str(&serialized).unwrap();
        prop_assert_eq!(score.convexity_ratio, deserialized.convexity_ratio);
    }
}
