use async_nats::Client as NatsClient;
use basilisk_bot::config::Config;
use basilisk_bot::data::{Cex<PERSON><PERSON>, ChainMonitor, FractalAnalyzer};
use basilisk_bot::shared_types::{NatsTopics, TemporalHarmonics, NetworkSeismologyReport, GeometricScore, FractalAnalysisReport};
use std::sync::Arc;
use std::error::Error;
use tokio::signal;
use tokio::sync::broadcast;
use futures::StreamExt;
use tracing::{info, warn, error, Level};
use tracing_subscriber::FmtSubscriber;
use serde_json;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Initialize logging
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;

    // Load configuration
    let settings = Config::load()?;


    // Connect to NATS
    // info!("Connecting to NATS server at {}", settings.nats.url);
    // let nats_client = async_nats::connect(&settings.nats.url).await?;
    // info!("Connected to NATS server");

    // Start CEX feeds
    // let mut cex_handles = Vec::new();
    // for cex_config in &settings.cex {
    //     let cex_feed = CexFeed::new(
    //         cex_config.name.clone(),
    //         cex_config.ws_url.clone(),
    //         cex_config.api_key.clone(),
    //         cex_config.api_secret.clone(),
    //         nats_client.clone(),
    //     );

    //     let handle = tokio::spawn(async move {
    //         if let Err(e) = cex_feed.start().await {
    //             eprintln!("CEX feed error: {}", e);
    //         }
    //     });

    //     cex_handles.push(handle);
    //     info!("Started CEX feed for {}", cex_config.name);
    // }

    // Start chain monitor
    let chain_id = settings.chains.keys().next().cloned().unwrap_or(8453);
    let rpc_url = settings.chains.get(&chain_id).and_then(|c| c.rpc_url.clone());

    if rpc_url.is_none() {
        error!("No RPC URL configured for chain ID {}", chain_id);
        return Err("RPC URL is required".into());
    }

    let nats_client = async_nats::connect("nats://localhost:4222").await?;
    let mut chain_monitor = ChainMonitor::new(
        chain_id,
        rpc_url.unwrap(),
        Arc::new(settings.clone()),
        nats_client.clone(),
    );

    let chain_handle = tokio::spawn(async move {
        if let Err(e) = chain_monitor.start().await {
            eprintln!("Chain monitor error: {}", e);
        }
    });

    info!("Started chain monitor for Base");

    // ZEN GEOMETER: Start Fractal Analyzer
    let mut fractal_analyzer = FractalAnalyzer::new();
    let nats_clone = nats_client.clone();
    let fractal_handle = tokio::spawn(async move {
        if let Err(e) = fractal_analyzer.listen_for_data(nats_clone).await {
            eprintln!("Fractal analyzer error: {}", e);
        }
    });

    info!("Started Fractal Analyzer (Zen Geometer Environmental Perception)");

    // Create broadcast channel for ARE data distribution
    let (are_sender, _are_receiver) = broadcast::channel::<AREDataEvent>(1000);
    
    // Start NATS message listener for ARE components
    // let are_listener_handle = start_are_data_listener(nats_client.clone(), settings.clone(), are_sender.clone()).await?;
    
    // info!("Started ARE data listener for live component integration");

    // Wait for Ctrl+C
    info!("Press Ctrl+C to stop");
    signal::ctrl_c().await?;
    info!("Shutting down...");

    // Cleanup
    // are_listener_handle.abort();
    // for handle in cex_handles {
    //     handle.abort();
    // }
    chain_handle.abort();
    fractal_handle.abort();

    Ok(())
}

// ARE Data Events for TUI integration
#[derive(Debug, Clone)]
pub enum AREDataEvent {
    TemporalHarmonics(TemporalHarmonics),
    NetworkSeismology(NetworkSeismologyReport),
    GeometricScore(GeometricScore),
    FractalAnalysis(FractalAnalysisReport),
    MarketData { pair: String, price: f64, volume: f64 },
    NetworkBlock { block_number: u64, timestamp: u64, gas_used: u64 },
    GasPrice { price_gwei: f64, timestamp: u64 },
}

// NATS message listener for ARE components
async fn start_are_data_listener(
    nats_client: NatsClient,
    settings: Config,
    broadcaster: broadcast::Sender<AREDataEvent>,
) -> Result<tokio::task::JoinHandle<()>, Box<dyn Error>> {
    let mut subscribers: Vec<async_nats::Subscriber> = Vec::new();
    
    // Subscribe to all configured subjects
    // for subject in &settings.nats.subjects {
    //     let subscriber = if let Some(ref queue_group) = settings.nats.queue_group {
    //         nats_client.queue_subscribe(subject.clone(), queue_group.clone()).await?
    //     } else {
    //         nats_client.subscribe(subject.clone()).await?
    //     };
    //     subscribers.push(subscriber);
    //     info!("Subscribed to NATS subject: {}", subject);
    // }

    // Combine all subscriber streams
    let mut message_stream = futures::stream::select_all(subscribers);
    
    let handle = tokio::spawn(async move {
        let mut message_count = 0u64;
        let mut last_stats_log = std::time::Instant::now();
        
        while let Some(message) = message_stream.next().await {
            message_count += 1;
            
            // Parse and route the message
            let subject = message.subject.to_string();
            match parse_nats_message(subject.clone(), &message.payload) {
                Ok(event) => {
                    if let Err(e) = broadcaster.send(event) {
                        warn!("Failed to broadcast ARE data event: {}", e);
                    }
                }
                Err(e) => {
                    error!("Failed to parse NATS message from {}: {}", subject, e);
                }
            }
            
            // Log statistics every 30 seconds
            if last_stats_log.elapsed().as_secs() >= 30 {
                info!("Processed {} NATS messages in last 30s", message_count);
                message_count = 0;
                last_stats_log = std::time::Instant::now();
            }
        }
        
        warn!("NATS message stream ended");
    });
    
    Ok(handle)
}

// Parse NATS messages into ARE data events
fn parse_nats_message(subject: String, payload: &[u8]) -> Result<AREDataEvent, Box<dyn Error>> {
    let payload_str = std::str::from_utf8(payload)?;
    
    match subject.as_str() {
        // ARE Component Data
        s if s.starts_with("are.temporal_harmonics") => {
            let harmonics: TemporalHarmonics = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::TemporalHarmonics(harmonics))
        }
        s if s.starts_with("are.network_seismology") => {
            let seismology: NetworkSeismologyReport = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::NetworkSeismology(seismology))
        }
        s if s.starts_with("are.geometric_score") => {
            let score: GeometricScore = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::GeometricScore(score))
        }
        s if s.starts_with("are.fractal_analysis") => {
            let analysis: FractalAnalysisReport = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::FractalAnalysis(analysis))
        }
        
        // Market Data
        s if s.starts_with("market.trades") => {
            let trade_data: serde_json::Value = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::MarketData {
                pair: trade_data["pair"].as_str().unwrap_or("UNKNOWN").to_string(),
                price: trade_data["price"].as_f64().unwrap_or(0.0),
                volume: trade_data["volume"].as_f64().unwrap_or(0.0),
            })
        }
        
        // Network Data
        s if s.starts_with("network.blocks") => {
            let block_data: serde_json::Value = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::NetworkBlock {
                block_number: block_data["number"].as_u64().unwrap_or(0),
                timestamp: block_data["timestamp"].as_u64().unwrap_or(0),
                gas_used: block_data["gasUsed"].as_u64().unwrap_or(0),
            })
        }
        
        // Gas Price Data
        s if s.starts_with("gas.prices") => {
            let gas_data: serde_json::Value = serde_json::from_str(payload_str)?;
            Ok(AREDataEvent::GasPrice {
                price_gwei: gas_data["price_gwei"].as_f64().unwrap_or(0.0),
                timestamp: gas_data["timestamp"].as_u64().unwrap_or(0),
            })
        }
        
        _ => {
            warn!("Unknown NATS subject: {}", subject);
            Err(format!("Unknown subject: {}", subject).into())
        }
    }
}
