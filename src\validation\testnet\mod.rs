//! Anvil Testnet Validation Framework
//! 
//! This module provides comprehensive validation capabilities for testing the Zen Geometer
//! autonomous trading bot on a local Anvil testnet environment using the `config/testnet.toml`
//! configuration. The validation framework systematically tests all trading strategies,
//! smart contract interactions, risk management systems, and infrastructure integrations
//! before production deployment.

pub mod framework;
pub mod config;
pub mod types;
pub mod results;

// Validation components
pub mod configuration_validator;
pub mod strategy_validator;
pub mod contract_validator;
pub mod risk_validator;
pub mod performance_monitor;
pub mod log_analyzer;
pub mod infrastructure_validator;
pub mod lifecycle_validator;
pub mod orchestrator;

// Re-exports
pub use framework::TestnetValidationFramework;
pub use config::{TestnetConfig, TestnetConfigLoader, TestnetValidationConfig};
pub use types::*;
pub use results::*;

pub use configuration_validator::{ConfigurationValidator, ConfigurationValidationResult};
pub use strategy_validator::{StrategyValidator, StrategyValidationResult};
pub use contract_validator::{ContractValidator, ContractValidationResult};
pub use risk_validator::{RiskValidator, RiskValidationResult};
pub use performance_monitor::{PerformanceMonitor, PerformanceValidationResult};
pub use log_analyzer::{LogAnalyzer, LogAnalysisResult};
pub use infrastructure_validator::{InfrastructureValidator, InfrastructureValidationResult};
pub use lifecycle_validator::{LifecycleValidator, LifecycleValidationResult};
pub use orchestrator::{ValidationOrchestrator, ValidationReport};

use crate::error::BasiliskError;

/// Result type for testnet validation operations
pub type TestnetValidationResult<T> = std::result::Result<T, BasiliskError>;