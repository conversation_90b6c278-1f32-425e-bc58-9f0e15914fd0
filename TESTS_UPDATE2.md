# Implementation Plan: Basilisk Bot Unit Test Suite & Code Coverage

This document outlines a phased implementation plan to build a comprehensive unit test suite for the Basilisk Bot, establish robust code coverage analysis, and integrate these practices into the development workflow.

---

### **Objective**

To achieve a high degree of confidence in the correctness and reliability of each individual component of the bot, enabling safer refactoring, faster development, and preparing the system for live trading.

### **Key Tools & Practices**

*   **Testing Framework:** `cargo test` (native Rust framework).
*   **Mocking Library:** `mockall` for creating mock objects to isolate dependencies.
*   **Coverage Tool:** `grcov` to generate code coverage reports.
*   **Test Pattern:** The "Arrange, Act, Assert" pattern will be used for structuring tests to ensure clarity and maintainability.

---

### **Implementation Phases**

The implementation is broken down into logical phases, starting with the core mathematical and logical components and expanding outward to the full system.

#### **Phase 0: Setup and Foundation**

**Goal:** Prepare the project and development environment for efficient testing and coverage analysis.

1.  **Install Tooling:**
    *   Install `grcov`: `cargo install grcov`
    *   Install LLVM tools: `rustup component add llvm-tools-preview`

2.  **Configure Project:**
    *   Add `mockall` and other testing utilities to the `[dev-dependencies]` section of `Cargo.toml`.
    *   Create a `.cargo/config.toml` file with flags to enable coverage instrumentation.
        ```toml
        [build]
        rustflags = ["-C", "instrument-coverage"]
        ```

3.  **Create Automation Script:**
    *   Create a new script at `scripts/run_coverage.sh`.
    *   This script will automate the process of cleaning, testing, and generating an HTML coverage report.
        ```bash
        #!/bin/bash
        # scripts/run_coverage.sh

        echo "Cleaning previous artifacts..."
        cargo clean

        echo "Setting coverage flags..."
        export CARGO_INCREMENTAL=0
        export RUSTFLAGS="-Cinstrument-coverage"
        export LLVM_PROFILE_FILE="basilisk_bot-%p-%m.profraw"

        echo "Running all tests..."
        cargo test --all-features

        echo "Generating coverage report..."
        grcov . --binary-path ./target/debug/ -s . -t html --branch --ignore-not-existing -o ./coverage/

        echo "Coverage report generated in ./coverage/index.html"
        ```

4.  **Establish Baseline:**
    *   Run the `run_coverage.sh` script for the first time to generate a baseline report. This will highlight the starting point and help track progress.

---

#### **Phase 1: Core Logic and Mathematics (The "Brain")**

**Goal:** Test the fundamental, pure-logic components of the bot. These have few dependencies and are critical for correct calculations.

*   **Scope:**
    *   `test_math_logic`
    *   `test_geometric_analysis`
    *   `test_vesica`
    *   `test_fractal_analyzer`
    *   `test_shared_types`
*   **Methodology:**
    *   Focus on pure functions with a wide range of inputs.
    *   Include tests for edge cases: zero, negative values, large numbers, empty inputs, etc.
    *   For complex mathematical models, consider using property-based testing with the `proptest` crate to cover a wider range of scenarios automatically.

---

#### **Phase 2: Strategy and Scoring (The "Decision Engine")**

**Goal:** Test the components responsible for evaluating opportunities and making trade decisions.

*   **Scope:**
    *   `test_strategy_scoring`
    *   `test_strategy_manager`
    *   `test_regime_manager`
    *   `test_swap_scanner`
    *   `test_asset_centrality`
*   **Methodology:**
    *   Use `mockall` to mock dependencies like data sources (`PriceProvider`), analytical pillars (`GeometricScorer`), and network states.
    *   Test how the `ScoringEngine` combines inputs from different pillars.
    *   Verify that the system correctly falls back to neutral scores when data is missing.
    *   Test the logic within the `StrategyManager` for selecting and prioritizing opportunities.

---

#### **Phase 3: Data Pipeline and Network Interaction (The "Senses")**

**Goal:** Test the components that ingest and process data from external sources.

*   **Scope:**
    *   `test_data_ingestion` & `test_data_handler`
    *   `test_block_ingestor`
    *   `test_chain_monitor`
    *   `test_price_oracle`
    *   `test_network_observer` & `test_seismic_analyzer`
*   **Methodology:**
    *   Mock network clients (e.g., NATS, RPC providers).
    *   Test data parsing logic for both valid and malformed data payloads.
    *   Verify error handling for network failures or invalid data.
    *   Test the state management logic (e.g., how `ChainMonitor` handles blockchain reorganizations).

---

#### **Phase 4: Execution and Risk Management (The "Hands")**

**Goal:** Test the final stages of the trade lifecycle: execution, dispatch, and risk checks.

*   **Scope:**
    *   `test_execution_manager`
    *   `test_execution_dispatcher`
    *   `test_risk_models`
    *   `test_portfolio_manager`
    *   `test_harmonic_timing_oracle`
*   **Methodology:**
    *   Mock blockchain interactions (e.g., `ethers::providers::Provider`).
    *   Test transaction construction, gas estimation logic, and nonce management.
    *   Verify that the `RiskManager` correctly enforces its rules (e.g., halting trading, checking position sizes).
    *   Test the `ExecutionManager`'s ability to handle transaction failures and retries.

---

#### **Phase 5: CLI, Configuration, and Auxiliaries (The "Interface")**

**Goal:** Test user-facing components and utilities to ensure they are reliable and function as expected.

*   **Scope:**
    *   `test_cli_handlers` & `test_cli_integration`
    *   `test_config_validation` & `test_config_loading`
    *   `test_backtester`
    *   `test_simulation_environment`
    *   `test_main_logic` (testing argument parsing and task spawning)
*   **Methodology:**
    *   Test CLI command parsing and handler logic.
    *   Verify that configuration files are loaded correctly and that validation rules catch invalid settings.
    *   Test the core logic of auxiliary binaries like the backtester and data generators.

---

#### **Phase 6: Continuous Improvement and Maintenance**

**Goal:** Embed testing and quality assurance into the daily development workflow.

1.  **CI/CD Integration:**
    *   Add a step to the GitHub Actions workflow to run the `scripts/run_coverage.sh` script on every pull request.
    *   Use a tool like `codecov.io` or `coveralls.io` to upload coverage reports and track trends over time.

2.  **Enforce Quality Gates:**
    *   Configure the CI pipeline to fail if code coverage drops below a predefined threshold (e.g., start with 60% and gradually increase to 80%+).
    *   Establish a team policy that new features or bug fixes must be accompanied by corresponding unit tests.

3.  **Ongoing Maintenance:**
    *   Periodically review and refactor tests to keep them clean, fast, and effective.
    *   As the project matures, continue to work through the more aspirational test modules listed in `tests/unit/mod.rs` to achieve near-complete coverage.
