# Complete Live Trading Deployment Process

## Overview

This guide provides a comprehensive step-by-step process for preparing the Zen Geometer (Basilisk Bot) for live trading operations. It covers infrastructure setup, contract deployment, configuration management, security validation, and the progressive deployment ladder.

## Prerequisites Checklist

Before starting the deployment process, ensure you have:

- [ ] **Rust Environment**: Latest stable Rust toolchain installed
- [ ] **Docker & Docker Compose**: For infrastructure services
- [ ] **Node.js & npm**: For smart contract deployment
- [ ] **Funded Wallet**: Sufficient ETH on Base mainnet for gas fees and trading capital
- [ ] **API Keys**: RPC endpoints, block explorers, and optional MEV relay access
- [ ] **Security Setup**: Dedicated trading wallet with appropriate security measures

## Phase 1: Environment and Security Setup

### 1.1 Create Production Environment File

Create your production environment configuration:

```bash
# Copy the example environment file
cp .env.example .env

# Edit with your production values
nano .env
```

**Required Environment Variables:**

```bash
# CRITICAL: Private key for transaction execution (without 0x prefix)
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here

# RPC Configuration (Use premium endpoints for production)
BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/YOUR_API_KEY
BASE_RPC_API_KEY=your_base_rpc_api_key

# Block Explorer API (for contract verification)
BASESCAN_API_KEY=your_basescan_api_key

# Database Configuration
DATABASE_URL=postgres://basilisk:basilisk_password@localhost:5432/basilisk_db

# Caching and Messaging
REDIS_URL=redis://localhost:6379
NATS_URL=nats://localhost:4222

# Optional: MEV Protection
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
TITAN_RELAY_URL=https://rpc.titanbuilder.xyz

# Optional: CEX Integration
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret

# Security
HONEYPOT_API_KEY=your_goplus_api_key

# Monitoring & Alerting
DISCORD_WEBHOOK_URL=your_discord_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### 1.2 Wallet Security Validation

**CRITICAL SECURITY STEPS:**

1. **Use a Dedicated Trading Wallet**:
   ```bash
   # Verify wallet address matches your private key
   cargo run -- utils wallet-info
   ```

2. **Fund the Wallet Appropriately**:
   ```bash
   # Check current balances
   cargo run -- utils balances --all-chains
   
   # Recommended minimum funding:
   # - 0.1 ETH for gas fees
   # - $1000-5000 USDC for initial trading capital
   ```

3. **Test Wallet Connectivity**:
   ```bash
   # Test wallet can sign transactions
   cargo run -- utils test-wallet --dry-run
   ```

### 1.3 API Key Validation

Verify all API keys are working:

```bash
# Test RPC connectivity
cargo run -- utils ping-nodes --all-chains

# Validate BaseScan API key
curl "https://api.basescan.org/api?module=account&action=balance&address=******************************************&tag=latest&apikey=$BASESCAN_API_KEY"
```

## Phase 2: Infrastructure Services Setup

### 2.1 Start Core Infrastructure Services

Start each service in a separate terminal to monitor logs:

**Terminal 1 - Database (TimescaleDB):**
```bash
docker-compose -f docker-compose.prod.yml up timescaledb
```

**Terminal 2 - Caching (Redis):**
```bash
docker-compose -f docker-compose.prod.yml up redis
```

**Terminal 3 - Messaging (NATS):**
```bash
docker-compose -f docker-compose.prod.yml up nats
```

### 2.2 Start Monitoring Services (Recommended)

**Terminal 4 - Metrics Collection (Prometheus):**
```bash
docker-compose -f docker-compose.prod.yml up prometheus
```

**Terminal 5 - Dashboards (Grafana):**
```bash
docker-compose -f docker-compose.prod.yml up grafana
```

**Terminal 6 - Log Aggregation (Loki):**
```bash
docker-compose -f docker-compose.prod.yml up loki
```

### 2.3 Verify Infrastructure Health

```bash
# Check all services are running
docker-compose -f docker-compose.prod.yml ps

# Test database connectivity
docker exec basilisk_timescaledb pg_isready -U basilisk -d basilisk_db

# Test Redis connectivity
docker exec basilisk_redis redis-cli ping

# Test NATS connectivity
curl -f http://localhost:8222/healthz

# Access monitoring dashboards
echo "Grafana: http://localhost:3000 (admin/basilisk_bot_admin)"
echo "Prometheus: http://localhost:9090"
echo "NATS Monitor: http://localhost:8222"
```

## Phase 3: Smart Contract Deployment

### 3.1 Prepare Contract Environment

```bash
cd geometer-contracts

# Create contract deployment environment
cp .env.example .env

# Edit with your production values
nano .env
```

**Contract Environment Variables:**
```bash
# CRITICAL: Same private key as main bot
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here

# RPC URLs for deployment
BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# API Keys for contract verification
BASESCAN_API_KEY=your_basescan_api_key

# Production contract addresses (auto-populated during deployment)
AAVE_POOL_ADDRESSES_PROVIDER=******************************************
STARGATE_ROUTER=******************************************

# Gas settings for deployment
GAS_PRICE_GWEI=1
GAS_LIMIT=5000000
```

### 3.2 Deploy Smart Contracts

```bash
# Install dependencies
npm install

# Compile contracts
npx hardhat compile

# Deploy to Base mainnet
./deploy-production.sh
```

**Expected Output:**
```
🚀 Deploying StargateCompassV1 to Base Mainnet...
✅ Environment validated
📡 RPC URL: https://base-mainnet.g.alchemy.com/v2/...
🔑 Using private key: 0x1234567890...
🔨 Deploying contracts...
✅ Deployment successful!
📋 Contract addresses saved in: ignition/deployments/chain-8453/
🔍 Verifying contracts on BaseScan...
```

### 3.3 Record Contract Addresses

**CRITICAL**: Copy the deployed contract addresses from the output. You'll need these for the next step.

Example addresses to record:
```
StargateCompassV1: 0x1234567890abcdef1234567890abcdef12345678
```

## Phase 4: Configuration Updates

### 4.1 Update Production Configuration

Edit `config/production.toml` with your deployed contract addresses:

```bash
cd .. # Return to main directory
nano config/production.toml
```

**Update the contracts section:**
```toml
[chains.8453.contracts]
# ... existing addresses ...
# UPDATE THIS with your deployed address
stargate_compass_v1 = "0x1234567890abcdef1234567890abcdef12345678"  # YOUR DEPLOYED ADDRESS
```

### 4.2 Validate Configuration

```bash
# Strict configuration validation
cargo run -- config validate --strict --check-network --security-audit

# Test configuration loading
cargo run -- config show --profile production

# Validate contract addresses are accessible
cargo run -- utils verify-contracts --chain-id 8453
```

## Phase 5: Pre-Flight System Validation

### 5.1 Run Comprehensive Pre-Flight Checks

```bash
# Run the complete preflight validation suite
./scripts/preflight.sh

# Expected output should show all green checkmarks:
# ✅ [SYSTEM] Rust toolchain validation
# ✅ [SYSTEM] Docker services health check
# ✅ [CONFIG] Configuration file validation
# ✅ [NETWORK] RPC endpoint connectivity
# ✅ [WALLET] Private key and balance validation
# ✅ [CONTRACTS] Smart contract accessibility
# ✅ [SECURITY] Security audit checks
```

### 5.2 Additional Validation Commands

```bash
# Check wallet balances across all chains
cargo run -- utils balances --all-chains

# Test network connectivity
cargo run -- utils ping-nodes --verbose

# Validate smart contract integration
cargo run -- utils test-contracts --chain-id 8453

# Security audit of configuration
cargo run -- config security-audit
```

## Phase 6: Progressive Deployment Ladder

### 6.1 Guided Deployment Process

Use the deployment ladder for safe progression:

```bash
# Interactive guided deployment
./scripts/deployment_ladder.sh
```

### 6.2 Manual Step-by-Step Deployment

**Step 1: Simulation Mode (No Risk)**
```bash
cargo run -- run --mode simulate --verbose

# Let it run for 10-15 minutes to observe:
# - Market data ingestion
# - Opportunity detection
# - Strategy scoring
# - Simulated execution decisions
```

**Step 2: Shadow Mode (No Risk)**
```bash
cargo run -- run --mode shadow --verbose

# Validates:
# - Fork-based transaction simulation
# - Gas estimation accuracy
# - Contract interaction validation
# - Profitability calculations
```

**Step 3: Sentinel Mode (Minimal Risk)**
```bash
cargo run -- run --mode sentinel --verbose

# Features:
# - Live monitoring with test transactions
# - Maximum $10 USD exposure
# - Contract health verification
# - Real gas cost validation
```

**Step 4: Low-Capital Mode (Low Risk)**
```bash
cargo run -- run --mode low-capital --verbose

# Conservative trading with:
# - Maximum $100 USD position size
# - Maximum $50 USD daily loss
# - Kelly fraction capped at 2%
# - Enhanced risk monitoring
```

**Step 5: Live Mode (Full Risk)**
```bash
# FINAL CONFIRMATION REQUIRED
cargo run -- run --mode live --verbose

# Full production trading with:
# - Complete strategy suite active
# - Production risk parameters
# - Real-time MEV protection
# - Comprehensive monitoring
```

## Phase 7: Production Monitoring Setup

### 7.1 Access Monitoring Dashboards

- **Grafana Dashboard**: http://localhost:3000
  - Username: `admin`
  - Password: `basilisk_bot_admin`
  - Pre-configured dashboards for trading metrics

- **Prometheus Metrics**: http://localhost:9090
  - Raw metrics and alerting rules
  - Custom queries for performance analysis

- **NATS Monitoring**: http://localhost:8222
  - Message flow monitoring
  - Connection health status

### 7.2 Set Up Alerting

Configure alerts for critical events:

```bash
# Test Discord webhook (if configured)
curl -X POST "$DISCORD_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{"content": "Basilisk Bot deployment test alert"}'

# Test Telegram bot (if configured)
curl -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
  -d "chat_id=$TELEGRAM_CHAT_ID&text=Basilisk Bot deployment test"
```

### 7.3 Log Monitoring

Monitor logs in real-time:

```bash
# Bot logs (when running)
tail -f logs/basilisk_bot.log

# Infrastructure logs
docker-compose -f docker-compose.prod.yml logs -f

# Specific service logs
docker-compose -f docker-compose.prod.yml logs -f timescaledb
docker-compose -f docker-compose.prod.yml logs -f redis
docker-compose -f docker-compose.prod.yml logs -f nats
```

## Phase 8: Emergency Procedures

### 8.1 Emergency Stop

```bash
# Immediate bot shutdown
pkill -f basilisk_bot

# Or use graceful shutdown
cargo run -- emergency-stop --reason "manual_intervention"
```

### 8.2 Position Recovery

```bash
# Check current positions
cargo run -- utils positions --all-chains

# Emergency position closure
cargo run -- emergency close-all-positions --confirm
```

### 8.3 Fund Recovery

```bash
# Withdraw all funds to safe wallet
cargo run -- emergency withdraw-all --to-address 0xYourSafeWalletAddress --confirm
```

## Configuration Checklist

### Pre-Deployment Validation

- [ ] **Environment Variables**: All required variables set and validated
- [ ] **Wallet Security**: Dedicated wallet with appropriate funding
- [ ] **API Keys**: All RPC and service API keys tested and working
- [ ] **Infrastructure**: All Docker services running and healthy
- [ ] **Smart Contracts**: Successfully deployed and verified on BaseScan
- [ ] **Configuration**: Production config updated with contract addresses
- [ ] **Pre-Flight Checks**: All preflight validations passing
- [ ] **Network Connectivity**: All RPC endpoints accessible and responsive
- [ ] **Monitoring**: Grafana dashboards accessible and displaying data

### Security Validation

- [ ] **Private Key Security**: Never committed to version control
- [ ] **Wallet Isolation**: Dedicated trading wallet, not personal wallet
- [ ] **API Key Rotation**: Fresh API keys generated for production
- [ ] **Access Controls**: Limited access to production environment
- [ ] **Backup Procedures**: Emergency recovery procedures documented
- [ ] **Monitoring Alerts**: Critical alerts configured and tested

### Operational Readiness

- [ ] **Risk Parameters**: Appropriate for your risk tolerance
- [ ] **Capital Allocation**: Reasonable position sizing for account size
- [ ] **Monitoring Setup**: Real-time dashboards and alerting active
- [ ] **Emergency Procedures**: Stop-loss and recovery procedures tested
- [ ] **Documentation**: All procedures documented and accessible
- [ ] **Team Readiness**: Operators trained on emergency procedures

## Troubleshooting Common Issues

### Infrastructure Issues

**Database Connection Failures:**
```bash
# Check if TimescaleDB is running
docker-compose -f docker-compose.prod.yml ps timescaledb

# Reset database if needed
docker-compose -f docker-compose.prod.yml down timescaledb
docker volume rm basilisk_timescaledb_data
docker-compose -f docker-compose.prod.yml up timescaledb
```

**NATS Connection Issues:**
```bash
# Check NATS health
curl http://localhost:8222/healthz

# Restart NATS if needed
docker-compose -f docker-compose.prod.yml restart nats
```

### Configuration Issues

**Contract Address Validation Failures:**
```bash
# Verify contract is deployed
cast code 0xYourContractAddress --rpc-url $BASE_RPC_URL

# Re-deploy if necessary
cd geometer-contracts && ./deploy-production.sh
```

**RPC Connectivity Issues:**
```bash
# Test RPC endpoint
curl -X POST $BASE_RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'

# Switch to backup RPC if needed
```

### Trading Issues

**Insufficient Balance Errors:**
```bash
# Check all balances
cargo run -- utils balances --all-chains

# Fund wallet if needed
# Transfer ETH and USDC to trading wallet
```

**High Gas Price Failures:**
```bash
# Check current gas prices
cargo run -- utils gas-prices --chain-id 8453

# Adjust gas parameters in config if needed
```

## Performance Optimization

### Production Performance Settings

For optimal performance in production:

```bash
# Build optimized binary
cargo build --release

# Use production configuration profile
export RUST_LOG=info
export RUST_BACKTRACE=1

# Run with performance optimizations
./target/release/basilisk_bot run --mode live
```

### Resource Monitoring

Monitor system resources:

```bash
# CPU and memory usage
htop

# Disk usage
df -h

# Network usage
iftop

# Docker resource usage
docker stats
```

## Maintenance Procedures

### Regular Maintenance Tasks

**Daily:**
- [ ] Check system health dashboards
- [ ] Review trading performance metrics
- [ ] Verify wallet balances
- [ ] Check for any error alerts

**Weekly:**
- [ ] Review and rotate logs
- [ ] Update API keys if needed
- [ ] Backup configuration and data
- [ ] Performance optimization review

**Monthly:**
- [ ] Security audit of access controls
- [ ] Update dependencies and security patches
- [ ] Review and optimize trading parameters
- [ ] Disaster recovery procedure testing

## Support and Resources

### Documentation References

- **User Guide**: `docs/USER_GUIDE.md`
- **Configuration Guide**: `docs/CONFIGURATION_GUIDE.md`
- **CLI Reference**: `docs/CLI_REFERENCE.md`
- **Production Architecture**: `docs/PRODUCTION_ARCHITECTURE.md`

### Emergency Contacts

- **Technical Support**: [Your technical support contact]
- **Security Issues**: [Your security team contact]
- **Infrastructure Issues**: [Your infrastructure team contact]

---

**⚠️ IMPORTANT SECURITY REMINDER ⚠️**

- Never share your private keys
- Always test in simulation mode first
- Start with small amounts in live trading
- Monitor positions continuously
- Have emergency procedures ready

**🚀 Ready for Live Trading!**

Once all checklist items are completed and validated, you're ready to begin live trading operations with the Zen Geometer bot.