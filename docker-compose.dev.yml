# version: '3.8'  # Removed obsolete version field

# Development environment overrides for Basilisk Bot
# Includes additional development tools and relaxed security

services:
  # Development instance of the bot
  basilisk_bot-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: basilisk_bot_dev
    # Increase memory limits for compilation
    mem_limit: 8g
    memswap_limit: 8g
    shm_size: 2g
    environment:
      RUST_LOG: debug
      RUST_BACKTRACE: 1
      ENVIRONMENT: development
      DRY_RUN: true
      NATS_URL: nats://nats:4222
      DATABASE_URL: ***********************************************************************/basilisk_bot
      REDIS_URL: redis://redis:6379
      # Development Features
      FRACTAL_ANALYSIS_ENABLED: true
      KELLY_CRITERION_ENABLED: true
      DYNAMIC_SLIPPAGE_ENABLED: true
      SIGINT_ENABLED: true
      # Development Safety
      MAX_POSITION_SIZE_USD: 100
      KELLY_FRACTION: 0.1 # Conservative for development
    volumes:
      - .:/workspace
      - ./config:/app/config
      - ./sigint:/app/sigint
      - ./logs:/app/logs
      # Remove problematic cargo cache volume - let container handle it internally
    working_dir: /workspace
    command: ['cargo', 'run', '--', 'run', '--dry-run']
    networks:
      - basilisk_bot_network
    depends_on:
      - nats
      - timescaledb
      - redis
    restart: 'no'
    labels:
      - 'com.basilisk_bot.service=trading_bot'
      - 'com.basilisk_bot.environment=development'

  # NATS CLI for debugging
  nats-cli:
    image: natsio/nats-box:latest
    container_name: basilisk_bot_nats_cli
    networks:
      - basilisk_bot_network
    command: ['sleep', 'infinity']
    labels:
      - 'com.basilisk_bot.service=debug_tools'
      - 'com.basilisk_bot.environment=development'

  # Redis CLI for debugging
  redis-cli:
    image: redis:7-alpine
    container_name: basilisk_bot_redis_cli
    networks:
      - basilisk_bot_network
    command: ['sleep', 'infinity']
    labels:
      - 'com.basilisk_bot.service=debug_tools'
      - 'com.basilisk_bot.environment=development'

  # PostgreSQL admin interface
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: basilisk_bot_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: admin@basilisk_bot.local
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - '5050:80'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - basilisk_bot_network
    depends_on:
      - timescaledb
    labels:
      - 'com.basilisk_bot.service=database_admin'
      - 'com.basilisk_bot.environment=development'

  # Log aggregation for development
  loki:
    image: grafana/loki:latest
    container_name: basilisk_bot_loki
    ports:
      - '3100:3100'
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - basilisk_bot_network
    labels:
      - 'com.basilisk_bot.service=log_aggregation'
      - 'com.basilisk_bot.environment=development'

volumes:
  cargo_cache:
    driver: local
  pgadmin_data:
    driver: local

networks:
  basilisk_bot_network:
    driver: bridge
