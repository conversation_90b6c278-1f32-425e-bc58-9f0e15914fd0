# Zen Geometer - Production Configuration
# Updated with latest verified addresses from official documentation
# Last updated: January 2025

# Global settings
app_name = "basilisk_bot"
log_level = "info"
dry_run = false
active_chain_id = 8453  # Base Network
authorized_operators = ["production_operator"]

[strategy]
kelly_fraction_cap = 0.25
min_profitability_bps = 50
enabled_strategies = ["zen_geometer"]

[database]
url = "postgres://basilisk:basilisk_password@localhost:5432/basilisk_db"

[redis]
url = "redis://localhost:6379"

[nats]
url = "nats://localhost:4222"

[rpc]
url = "https://mainnet.base.org"
ws_url = "wss://mainnet.base.org"

[execution]
max_slippage_bps = 300
gas_limit_multiplier = 1.2
max_gas_price_gwei = 50
gas_limit = 500000
max_priority_fee = 2000000000
default_slippage_tolerance = "0.005"
fallback_base_fee_gwei = 10.0
min_net_profit_threshold = 5.0
gas_multiplier_network_shock = 1.5
gas_multiplier_high_sp_time = 1.2
gas_multiplier_bot_gas_war = 2.0
gas_multiplier_retail_fomo = 1.1
gas_multiplier_high_volatility = 1.05
network_shock_threshold_ms = 500
stargate_compass_address = "******************************************"
chain_id = 8453
private_key = "${BASILISK_EXECUTION_PRIVATE_KEY}"

[execution.gas_buffer_multipliers]
low = "1.1"
medium = "1.2"
high = "1.3"
critical = "1.5"

[execution.priority_fee_gwei]
low_gwei = "1.0"
medium_gwei = "2.0"
high_gwei = "5.0"
critical_gwei = "10.0"

[risk]
max_position_size = "10.0"
max_slippage = "0.005"
max_gas_price = 100000000000
kelly_fraction = "0.25"
max_daily_loss = "1000.0"
max_strategy_loss = "500.0"
max_daily_loss_usd = "1000.0"
kelly_fraction_config = "0.25"
default_max_position_size_usd = "10000.0"
max_consecutive_failures = 5

[risk.regime_multipliers]
high_volatility_position_mult = "0.5"
high_volatility_loss_mult = "0.25"
bot_gas_war_position_mult = "0.1"
bot_gas_war_loss_mult = "0.1"
retail_fomo_position_mult = "1.2"
retail_fomo_loss_mult = "1.5"
calm_orderly_position_mult = "1.0"
calm_orderly_loss_mult = "1.0"

[strategies.unified]
enabled = true
min_execution_score = "0.5"
gaze_min_profit_usd = "10.0"
max_flash_exposure_usd = "50000.0"
bidding_aggressiveness_pct = "0.382"
min_net_profit_usd = "10.0"
risk_aversion_k = "0.5"
min_quality_ratio = "0.3"

[strategies]
min_execution_score = "0.5"
quality_ratio_floor = "0.3"

[strategies.basilisk_gaze]
enabled = false
pool_a_address = "******************************************"
pool_b_address = "******************************************"
core_asset_address = "******************************************"
test_amount_in_wei = "1000000000000000000"
min_profit_threshold_usd = "5.0"

[strategies.zen_geometer]
enabled = true
min_net_profit_usd = "10.0"

[strategies.unified.basilisk_gaze]
enabled = false
pool_a_address = "******************************************"
pool_b_address = "******************************************"
core_asset_address = "******************************************"
test_amount_in_wei = "1000000000000000000"
min_profit_threshold_usd = "5.0"

[alerting]
min_profit_threshold = "10.0"
max_revert_rate = "0.1"
max_slippage_percent = "0.05"

[aetheric_resonance]
min_resonance_score = 0.0

[scoring]
quality_ratio_floor = 0.3
risk_aversion_k = 0.5
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5
temporal_harmonics_weight = 0.33
geometric_score_weight = 0.33
network_resonance_weight = 0.34

[secrets]
# Secrets are loaded from environment variables with APP_SECRETS__ prefix

[scanners.gaze_scanner]
min_price_deviation_pct = "0.01"
block_check_delay_ms = 100

[scanners.pilot_fish_scanner]
min_whale_trade_usd = "50000.0"
min_price_impact_usd = "5000.0"
min_expected_profit_usd = "10.0"
profit_multiplier = "0.1"
default_volatility = "0.02"

[manifold]
worthy_assets = ["WETH", "USDC", "DEGEN"]

[bridges]
routes = [
    [8453, 666666666, 5.0, 180],    # Base -> Degen
]

[[cex]]
name = "coinbase"
ws_url = "wss://ws-feed.exchange.coinbase.com"

[chainlink_feeds]

# ===== PRODUCTION CHAIN CONFIGURATIONS =====

# Base Mainnet (Settlement Layer) - Production Addresses
[chains.8453]
name = "Base"
enabled = true
native_currency = "ETH"
max_gas_price = 50000000000  # 50 gwei

# Multiple RPC endpoints for redundancy (OROBOROS protocol)
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[[chains.8453.rpc_endpoints]]
url = "https://base.publicnode.com"
priority = 1

[[chains.8453.rpc_endpoints]]
url = "https://base.drpc.org"
priority = 2

[[chains.8453.rpc_endpoints]]
url = "https://1rpc.io/base"
priority = 3

# Production token addresses on Base
[chains.8453.tokens]
USDC = "******************************************"  # Native USDC on Base
WETH = "******************************************"  # Wrapped ETH on Base
DAI = "******************************************"   # DAI on Base
USDT = "******************************************"  # Tether USD on Base

# Production DEX addresses on Base (verified from docs)
[chains.8453.dex]
# Verified from MULTICHAIN_REFERENCE.md
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
uniswap_universal_router = "******************************************"
aerodrome_factory = "******************************************"
aerodrome_router = "******************************************"
aerodrome_weth_usdc_pool = "******************************************"
sushiswap_factory = "******************************************"
sushiswap_router = "******************************************"
balancer_vault = "******************************************"

# Production contract addresses on Base
[chains.8453.contracts]
multicall = "******************************************"
weth9 = "******************************************"
permit2 = "******************************************"
# Aave V3 addresses on Base (verified from Aave docs)
aave_pool_addresses_provider = "******************************************"
aave_pool = "******************************************"
# Stargate V1 addresses on Base (verified from Stargate docs)
stargate_router = "******************************************"
stargate_factory = "******************************************"
# TODO: Deploy StargateCompassV1 with production addresses
stargate_compass_v1 = "******************************************"  # DEPLOYED

# Degen Chain (Execution Layer) - Production Addresses
[chains.666666666]
name = "Degen"
enabled = true
native_currency = "DEGEN"
max_gas_price = 100000000000  # 100 gwei

# Degen Chain RPC endpoints
[[chains.666666666.rpc_endpoints]]
url = "https://rpc.degen.tips"
priority = 0

[[chains.666666666.rpc_endpoints]]
url = "https://rpc-degen-mainnet-1.t.conduit.xyz"
priority = 1

# Production token addresses on Degen Chain
[chains.666666666.tokens]
DEGEN = "******************************************"  # Native DEGEN token
USDC = "******************************************"   # Bridged USDC
WETH = "******************************************"   # Wrapped ETH

# Production DEX addresses on Degen Chain
[chains.666666666.dex]
degen_swap_router = "******************************************"  # Uniswap V2 fork
degen_swap_factory = "******************************************"

# Production contract addresses on Degen Chain
[chains.666666666.contracts]
multicall = "******************************************"
weth9 = "******************************************"
# Stargate V1 addresses on Degen Chain
stargate_router = "******************************************"