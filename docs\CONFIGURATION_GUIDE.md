# Zen Geometer Configuration Guide

This comprehensive guide covers all aspects of configuring the Zen Geometer autonomous trading system, from basic setup to advanced multi-chain deployment with complete parameter documentation.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Environment Variables](#environment-variables)
3. [Configuration Structure](#configuration-structure)
4. [Multi-Chain Setup](#multi-chain-setup)
5. [Strategy Configuration](#strategy-configuration)
6. [Risk Management](#risk-management)
7. [Execution Parameters](#execution-parameters)
8. [Infrastructure Configuration](#infrastructure-configuration)
9. [Security Configuration](#security-configuration)
10. [Profile Management](#profile-management)
11. [Validation and Testing](#validation-and-testing)
12. [Troubleshooting](#troubleshooting)

## Quick Start

### For New Users

The fastest way to get started is with a pre-configured template:

```bash
# Copy example environment file
cp .env.example .env
# Edit .env with your private key and API keys

# Use default configuration for Base Sepolia testnet
cargo run -- config validate --check-network

# Start in simulation mode (no risk)
cargo run -- run --mode simulate --verbose
```

### For Experienced Users

```bash
# Use production configuration template
cp config/production.toml config/my-config.toml
# Edit my-config.toml for your needs

# Validate configuration
cargo run -- config validate --config my-config.toml --strict

# Deploy using the 5-tier ladder
./scripts/deployment_ladder.sh
```

### Configuration Files Overview

The Zen Geometer uses a hierarchical configuration system:

- **Primary Config**: `config/default.toml` (Base Sepolia testnet)
- **Production Config**: `config/production.toml` (Base mainnet + Degen Chain)
- **Local Testing**: `config/testnet.toml` (Anvil fork)
- **Environment Variables**: `.env` (sensitive data)

## Environment Variables

All sensitive configuration is managed through environment variables for security.

### Required Variables

```bash
# CRITICAL: Private key for transaction execution (without 0x prefix)
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here
```

### Optional RPC API Keys

```bash
# Recommended for production to avoid rate limits
BASE_RPC_API_KEY=your_base_rpc_api_key
ARBITRUM_RPC_API_KEY=your_arbitrum_rpc_api_key
POLYGON_RPC_API_KEY=your_polygon_rpc_api_key
```

### CEX Integration (Optional)

```bash
# For enhanced market data and execution
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret
```

### MEV Protection (Optional)

```bash
# Private relay endpoints for MEV protection
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
TITAN_RELAY_URL=https://rpc.titanbuilder.xyz
```

### Infrastructure Overrides

```bash
# Override default infrastructure endpoints
DATABASE_URL=******************************/db
REDIS_URL=redis://host:6379
NATS_URL=nats://host:4222
```

### Security & Monitoring

```bash
# Honeypot detection service
HONEYPOT_API_KEY=your_goplus_api_key

# Alert notifications
DISCORD_WEBHOOK_URL=your_discord_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

## New Layered Configuration System

The Zen Geometer now uses an advanced layered configuration system that provides maximum flexibility and security. This system loads configuration in the following order of precedence (later layers override earlier ones):

1. **Default values** - Built into the application
2. **Base configuration file** - `config/default.toml`
3. **Environment-specific file** - `config/production.toml`, `config/staging.toml`, etc.
4. **Environment variables** - With `APP_` prefix

### Environment Variable Mapping

The new system uses a structured approach to environment variables with the `APP_` prefix and `__` separators:

```bash
# Strategy configuration
APP_STRATEGY__KELLY_FRACTION_CAP=0.25
APP_STRATEGY__MIN_PROFITABILITY_BPS=50
APP_STRATEGY__ENABLED_STRATEGIES="gaze,zen_geometer"

# Execution configuration
APP_EXECUTION__MAX_SLIPPAGE_BPS=300
APP_EXECUTION__MAX_GAS_PRICE_GWEI=50

# Secrets (recommended approach)
APP_SECRETS__PRIVATE_KEYS__EXECUTION=your_private_key_here
APP_SECRETS__PRIVATE_KEYS__BASE=your_base_private_key
APP_SECRETS__API_KEYS__BINANCE=your_binance_api_key

# Chain configuration
APP_CHAINS__8453__RPC_URL=https://mainnet.base.org
APP_CHAINS__8453__MAX_GAS_PRICE=50000000000

# Infrastructure
APP_NATS__URL=nats://localhost:4222
APP_DATABASE__URL=postgres://user:pass@host/db
```

### Configuration Loading Examples

```bash
# Load default configuration
cargo run -- run --mode simulate

# Load with environment-specific overrides
APP_ENV=production cargo run -- run --mode live

# Load with custom config file
CONFIG_PATH=config/my-custom.toml cargo run -- run --mode shadow

# Combine custom file with environment overrides
CONFIG_PATH=config/my-custom.toml APP_STRATEGY__KELLY_FRACTION_CAP=0.15 cargo run -- run --mode live
```

### Backward Compatibility

The system maintains backward compatibility with legacy environment variables:

```bash
# Legacy format (still supported)
BASILISK_EXECUTION_PRIVATE_KEY=your_private_key

# New format (recommended)
APP_SECRETS__PRIVATE_KEYS__EXECUTION=your_private_key
```

The system will check both formats, with the new format taking precedence.

### Configuration Validation

The enhanced validation system provides comprehensive checks:

```bash
# Validate current configuration
cargo run -- config validate

# Validate with environment variables
APP_ENV=production cargo run -- config validate --strict

# Check specific configuration file
CONFIG_PATH=config/production.toml cargo run -- config validate
```

## Configuration Structure

The Zen Geometer uses a hierarchical TOML configuration system with the following main sections:

### Global Settings

```toml
# Global operational parameters
dry_run = false                    # Set to true for simulation mode
active_chain_id = 8453            # Primary chain ID (Base = 8453, Degen = *********)
authorized_operators = ["production_operator"]  # Authorized operator identities
```

### Core Configuration Sections

1. **[database]** - PostgreSQL database connection
2. **[redis]** - Redis cache configuration
3. **[nats]** - NATS messaging system
4. **[rpc]** - Primary RPC endpoint configuration
5. **[execution]** - Transaction execution parameters
6. **[risk]** - Risk management settings
7. **[strategies]** - Trading strategy configuration
8. **[chains]** - Multi-chain network definitions
9. **[scanners]** - Opportunity detection scanners
10. **[scoring]** - Aetheric Resonance Engine scoring weights

## Profile Management

Profiles allow you to manage multiple configurations for different use cases.

### Basic Commands

```bash
# List all profiles
cargo run -- config profile list

# Show detailed profile information
cargo run -- config profile show my-profile

# Create a new profile
cargo run -- config profile create my-profile

# Copy an existing profile
cargo run -- config profile copy source-profile target-profile

# Delete a profile
cargo run -- config profile delete old-profile

# Search profiles
cargo run -- config profile search "production"

# Show profile statistics
cargo run -- config profile stats
```

### Built-in Templates

The system includes several built-in templates:

#### Beginner Template

- **Use Case**: Learning and experimentation
- **Risk Level**: Conservative
- **Trading Mode**: Dry run only
- **Features**: Safe defaults, low risk limits

```bash
cargo run -- config profile show beginner
```

#### Development Template

- **Use Case**: Development and testing
- **Risk Level**: Moderate
- **Trading Mode**: Testnet
- **Features**: Development-friendly settings

#### Production Conservative

- **Use Case**: Live trading with safety
- **Risk Level**: Conservative
- **Trading Mode**: Mainnet
- **Features**: Low risk, steady profits

#### Production Aggressive

- **Use Case**: Higher risk/reward trading
- **Risk Level**: Aggressive
- **Trading Mode**: Mainnet
- **Features**: Higher position sizes, lower profit thresholds

#### High Frequency

- **Use Case**: Speed-optimized trading
- **Risk Level**: Moderate
- **Trading Mode**: Mainnet
- **Features**: Fast execution, optimized gas settings

## Configuration Structure

### Core Sections

#### Database Configuration

```toml
[database]
url = "postgres://user:password@localhost:5432/basilisk_db"
```

#### Redis Configuration

```toml
[redis]
url = "redis://localhost:6379"
```

#### NATS Configuration

```toml
[nats]
url = "nats://localhost:4222"
```

#### Execution Configuration

```toml
[execution]
gas_limit = 500000
max_priority_fee = 2000000000  # 2 gwei
default_slippage_tolerance = 0.005  # 0.5%

[execution.gas_buffer_multipliers]
low = 1.1
medium = 1.2
high = 1.3
critical = 1.5

[execution.priority_fee_gwei]
low_gwei = 1.0
medium_gwei = 2.0
high_gwei = 5.0
critical_gwei = 10.0
```

#### Risk Management

```toml
[risk]
max_daily_loss_usd = 100.0
kelly_fraction_config = 0.25
default_max_position_size_usd = 1000.0
max_consecutive_failures = 5
max_slippage = 0.005

[risk.regime_multipliers]
high_volatility_position_mult = 0.5
high_volatility_loss_mult = 0.8
bot_gas_war_position_mult = 0.3
bot_gas_war_loss_mult = 0.9
retail_fomo_position_mult = 1.2
retail_fomo_loss_mult = 1.1
calm_orderly_position_mult = 1.0
calm_orderly_loss_mult = 1.0
```

#### Strategy Configuration

```toml
[strategies.unified]
enabled = true
min_net_profit_usd = 2.0
risk_aversion_k = 0.5
max_flash_exposure_usd = 10000.0
bidding_aggressiveness_pct = 0.382  # Golden ratio
min_quality_ratio = 0.3

[strategies.zen_geometer]
enabled = true
min_net_profit_usd = 1.0
```

#### Chain Configuration

```toml
active_chain_id = 8453  # Base Network

[chains.8453]  # Base
name = "Base"
enabled = true
native_currency = "ETH"

[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[chains.8453.tokens]
WETH = "******************************************"
USDC = "******************************************"

[chains.8453.dex]
uniswap_v3_factory = "******************************************"
aerodrome_factory = "******************************************"
```

#### Scanner Configuration

```toml
[scanners.gaze_scanner]
min_price_deviation_pct = 0.01
block_check_delay_ms = 100

[scanners.pilot_fish_scanner]
min_whale_trade_usd = 10000.0
min_price_impact_usd = 1000.0
min_expected_profit_usd = 5.0
profit_multiplier = 0.1
default_volatility = 0.02
```

### Environment Variables

Sensitive configuration should be set via environment variables:

```bash
# Required for live trading
export BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here

# Optional RPC API keys
export BASE_RPC_API_KEY=your_base_api_key
export ARBITRUM_RPC_API_KEY=your_arbitrum_api_key

# Optional CEX credentials
export COINBASE_API_KEY=your_coinbase_key
export COINBASE_API_SECRET=your_coinbase_secret

# Optional MEV relay
export FLASHBOTS_RELAY_URL=https://relay.flashbots.net

# Infrastructure overrides
export DATABASE_URL=postgres://user:pass@host/db
export REDIS_URL=redis://host:port
export NATS_URL=nats://host:port
```

## Validation and Testing

### Configuration Validation

The system provides comprehensive validation with detailed feedback:

```bash
# Basic validation
cargo run -- config validate

# Strict validation with network checks
cargo run -- config validate --strict --check-network

# Output validation results as JSON
cargo run -- config validate --format json

# Validate a specific profile
cargo run -- config profile validate my-profile --strict
```

### Validation Features

- **Syntax Validation**: Ensures all values are properly formatted
- **Range Validation**: Checks that numeric values are within acceptable ranges
- **Cross-field Validation**: Validates relationships between different settings
- **Network Connectivity**: Tests connections to RPC endpoints and services
- **Security Checks**: Warns about insecure configurations
- **Performance Optimization**: Suggests improvements for better performance

### Validation Output

The validator provides:

- **Quality Score**: 0-100 rating of configuration quality
- **Error Classification**: Critical, High, Medium, Low severity levels
- **Fix Suggestions**: Specific recommendations for resolving issues
- **Warnings**: Non-critical issues that may affect performance
- **Optimization Suggestions**: Recommendations for better configuration

### Testing Configuration

```bash
# Test system connectivity
cargo run -- validate

# Test with specific configuration
cargo run -- validate --config my-profile.toml

# Check wallet balances
cargo run -- utils balances

# Ping all services
cargo run -- utils ping-nodes

# Test strategy with simulation
cargo run -- utils test-gaze --cycles 10
```

## Advanced Configuration

### Multi-Chain Setup

Configure multiple chains for cross-chain arbitrage:

```toml
active_chain_id = 8453

# Base configuration
[chains.8453]
name = "Base"
enabled = true
# ... configuration

# Arbitrum configuration
[chains.42161]
name = "Arbitrum"
enabled = true
# ... configuration

# Bridge routes for cross-chain operations
[bridges]
routes = [
    [8453, 42161, 5.0, 300],  # Base -> Arbitrum: $5 cost, 5min latency
]
```

### Custom Strategy Parameters

Fine-tune strategy behavior:

```toml
[strategies.unified]
# Core thresholds
min_execution_score = 0.7
min_net_profit_usd = 3.0
risk_aversion_k = 0.4

# Advanced parameters
bidding_aggressiveness_pct = 0.618  # Custom ratio
max_flash_exposure_usd = 25000.0
min_quality_ratio = 0.4

[scoring]
# Scoring weights (should sum to ~1.0)
temporal_harmonics_weight = 0.33
geometric_score_weight = 0.33
network_resonance_weight = 0.34

# Regime multipliers
regime_multiplier_retail_fomo = 1.2
regime_multiplier_high_vol = 0.8
regime_multiplier_calm = 1.0
regime_multiplier_gas_war_penalty = 0.5
```

### Performance Optimization

For high-frequency trading:

```toml
[execution]
gas_limit = 300000  # Lower for speed
default_slippage_tolerance = 0.003  # Tighter slippage

[scanners.gaze_scanner]
block_check_delay_ms = 50  # Faster scanning

[execution.priority_fee_gwei]
low_gwei = 2.0    # Higher base fees
medium_gwei = 5.0
high_gwei = 10.0
critical_gwei = 20.0
```

### Security Hardening

For production environments:

```toml
# Use multiple RPC endpoints for redundancy
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[[chains.8453.rpc_endpoints]]
url = "https://base-rpc.publicnode.com"
priority = 1

# Conservative risk settings
[risk]
max_daily_loss_usd = 100.0
kelly_fraction_config = 0.15
max_consecutive_failures = 3

# Enable all safety features
dry_run = false  # Only after thorough testing
```

## Import/Export

### Supported Formats

The system supports multiple configuration formats:

- **TOML**: Default format, human-readable
- **JSON**: Machine-readable, good for APIs
- **YAML**: Alternative human-readable format

### Export Examples

```bash
# Export as TOML (default)
cargo run -- config export my-profile --path config.toml

# Export as JSON
cargo run -- config export my-profile --format json --path config.json

# Export to stdout
cargo run -- config export my-profile

# Export full profile with metadata
cargo run -- config export my-profile --format profile --path full-profile.toml
```

### Import Examples

```bash
# Import TOML configuration
cargo run -- config import --path config.toml --name imported-config

# Import JSON configuration
cargo run -- config import --path config.json --name json-config --format json

# Auto-detect format
cargo run -- config import --path config.yaml --name yaml-config --format auto
```

## Troubleshooting

### Common Issues

#### Configuration Validation Errors

**Issue**: "Kelly fraction cannot exceed 1.0"

```bash
# Fix: Reduce kelly_fraction_config
[risk]
kelly_fraction_config = 0.25  # Must be ≤ 1.0
```

**Issue**: "RPC URL appears to be invalid"

```bash
# Fix: Use proper HTTP/HTTPS URL
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"  # Must start with http/https
```

**Issue**: "Private key required for live trading"

```bash
# Fix: Set environment variable
export BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_here
```

#### Network Connectivity Issues

**Issue**: "Cannot connect to NATS server"

```bash
# Check if NATS is running
docker-compose up -d nats

# Or use different URL
[nats]
url = "nats://your-nats-server:4222"
```

**Issue**: "RPC connection failed"

```bash
# Test RPC endpoint
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  https://mainnet.base.org

# Add fallback endpoints
[[chains.8453.rpc_endpoints]]
url = "https://base-rpc.publicnode.com"
priority = 1
```

#### Profile Management Issues

**Issue**: "Profile does not exist"

```bash
# List available profiles
cargo run -- config profile list

# Create missing profile
cargo run -- config profile create my-profile
```

**Issue**: "Configuration validation failed"

```bash
# Get detailed validation report
cargo run -- config profile validate my-profile --strict

# Fix issues and re-validate
cargo run -- config validate --format json > validation-report.json
```

### Getting Help

1. **Validation Reports**: Use `--format json` for detailed error analysis
2. **Profile Statistics**: Use `config profile stats` to understand your setup
3. **Search Functionality**: Use `config profile search` to find similar configurations
4. **Templates**: Start with built-in templates and customize as needed

### Best Practices

1. **Start Conservative**: Begin with low risk limits and increase gradually
2. **Test Thoroughly**: Always test in dry-run mode before live trading
3. **Use Profiles**: Maintain separate profiles for different strategies
4. **Regular Validation**: Validate configuration after any changes
5. **Backup Configurations**: Export important profiles regularly
6. **Monitor Performance**: Use validation scores to track configuration quality

## Configuration Examples

### Beginner Setup

```toml
# Safe configuration for learning
dry_run = true
active_chain_id = 8453

[risk]
max_daily_loss_usd = 50.0
kelly_fraction_config = 0.1

[strategies.unified]
min_net_profit_usd = 5.0
risk_aversion_k = 0.8
```

### Production Setup

```toml
# Live trading configuration
dry_run = false
active_chain_id = 8453

[risk]
max_daily_loss_usd = 500.0
kelly_fraction_config = 0.25

[strategies.unified]
min_net_profit_usd = 2.0
risk_aversion_k = 0.5
```

### High-Frequency Setup

```toml
# Speed-optimized configuration
[execution]
gas_limit = 300000
default_slippage_tolerance = 0.003

[scanners.gaze_scanner]
block_check_delay_ms = 50

[strategies.unified]
min_net_profit_usd = 0.5
bidding_aggressiveness_pct = 0.618
```

## Multi-Chain Setup

The Zen Geometer supports multi-chain arbitrage across Base, Arbitrum, and Degen Chain networks. Each chain requires specific configuration for tokens, DEXs, and contracts.

### Supported Networks

#### Base Network (Chain ID: 8453)

- **Role**: Primary settlement layer
- **Native Currency**: ETH
- **Primary DEXs**: Uniswap V3, Aerodrome
- **Bridge Protocol**: Stargate V1

#### Degen Chain (Chain ID: *********)

- **Role**: Execution layer for high-frequency operations
- **Native Currency**: DEGEN
- **Primary DEXs**: DegenSwap (Uniswap V2 fork)
- **Bridge Protocol**: Stargate V1

#### Arbitrum (Chain ID: 42161) - Future Support

- **Role**: Additional arbitrage opportunities
- **Native Currency**: ETH
- **Primary DEXs**: Uniswap V3, SushiSwap
- **Bridge Protocol**: Stargate V1

### Base Network Configuration

```toml
[chains.8453]
name = "Base"
enabled = true
native_currency = "ETH"

# Multiple RPC endpoints for redundancy
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[[chains.8453.rpc_endpoints]]
url = "https://base.publicnode.com"
priority = 1

[[chains.8453.rpc_endpoints]]
url = "https://base.drpc.org"
priority = 2

# Production token addresses
[chains.8453.tokens]
USDC = "******************************************"  # Native USDC
WETH = "******************************************"  # Wrapped ETH
DAI = "******************************************"   # DAI
USDT = "******************************************"  # Tether USD

# DEX configurations
[chains.8453.dex]
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
aerodrome_factory = "******************************************"
aerodrome_router = "******************************************"

# Smart contract addresses
[chains.8453.contracts]
multicall = "******************************************"
stargate_router = "******************************************"
stargate_compass_v1 = "******************************************"
aave_pool = "******************************************"
```

### Degen Chain Configuration

```toml
[chains.*********]
name = "Degen"
enabled = true
native_currency = "DEGEN"

# Degen Chain RPC endpoints
[[chains.*********.rpc_endpoints]]
url = "https://rpc.degen.tips"
priority = 0

[[chains.*********.rpc_endpoints]]
url = "https://rpc-degen-mainnet-1.t.conduit.xyz"
priority = 1

# Token addresses on Degen Chain
[chains.*********.tokens]
DEGEN = "******************************************"  # Native DEGEN
USDC = "******************************************"   # Bridged USDC
WETH = "******************************************"   # Wrapped ETH

# DEX configuration
[chains.*********.dex]
degen_swap_router = "******************************************"
degen_swap_factory = "******************************************"

# Contract addresses
[chains.*********.contracts]
multicall = "******************************************"
stargate_router = "******************************************"
```

### Arbitrum Configuration (Future)

```toml
[chains.42161]
name = "Arbitrum"
enabled = false  # Enable when ready for Arbitrum support
native_currency = "ETH"

# Arbitrum RPC endpoints
[[chains.42161.rpc_endpoints]]
url = "https://arb1.arbitrum.io/rpc"
priority = 0

[[chains.42161.rpc_endpoints]]
url = "https://arbitrum.publicnode.com"
priority = 1

# Token addresses on Arbitrum
[chains.42161.tokens]
USDC = "******************************************"  # Native USDC
WETH = "******************************************"  # Wrapped ETH
ARB = "******************************************"   # Arbitrum token

# DEX configuration
[chains.42161.dex]
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"
sushiswap_factory = "******************************************"

# Contract addresses
[chains.42161.contracts]
multicall = "******************************************"
stargate_router = "******************************************"
```

### Cross-Chain Bridge Configuration

```toml
# Bridge routes for cross-chain arbitrage
[bridges]
routes = [
    [8453, *********, 5.0, 180],    # Base -> Degen: $5 cost, 3min latency
    [8453, 42161, 8.0, 600],        # Base -> Arbitrum: $8 cost, 10min latency (future)
]
```

### Multi-Chain Setup Procedures

#### 1. Base Network Setup

```bash
# Validate Base network configuration
cargo run -- config validate --chain 8453

# Test Base network connectivity
cargo run -- utils ping-nodes --chain base

# Check Base wallet balance
cargo run -- utils balances --chain base
```

#### 2. Degen Chain Setup

```bash
# Validate Degen network configuration
cargo run -- config validate --chain *********

# Test Degen network connectivity
cargo run -- utils ping-nodes --chain degen

# Check Degen wallet balance
cargo run -- utils balances --chain degen
```

#### 3. Cross-Chain Bridge Testing

```bash
# Test Stargate bridge connectivity
cargo run -- utils test-bridge --from base --to degen --amount 0.01

# Validate cross-chain routing
cargo run -- utils validate-routes
```

## Strategy Configuration

The Zen Geometer implements multiple trading strategies that can be configured independently.

### Unified Strategy System

```toml
[strategies.unified]
enabled = true
min_execution_score = 0.5              # Minimum score to execute (0.0-1.0)
min_net_profit_usd = 5.0              # Minimum profit threshold in USD
risk_aversion_k = 0.5                 # Risk aversion parameter (0.0-1.0)
max_flash_exposure_usd = 50000.0      # Maximum flash loan exposure
bidding_aggressiveness_pct = 0.382    # Golden ratio bidding (0.0-1.0)
min_quality_ratio = 0.3               # Minimum quality threshold
```

### Individual Strategy Configuration

#### Zen Geometer (Cross-Chain Arbitrage)

```toml
[strategies.zen_geometer]
enabled = true
min_net_profit_usd = 10.0             # Higher threshold for cross-chain
```

#### Basilisk Gaze (DEX Arbitrage)

```toml
[strategies.basilisk_gaze]
enabled = true
min_profit_threshold_usd = 5.0        # Minimum profit for DEX arbitrage
test_amount_in_wei = "1000000000000000000"  # 1 ETH test amount
```

### Scanner Configuration

#### Gaze Scanner (Real-time Opportunity Detection)

```toml
[scanners.gaze_scanner]
min_price_deviation_pct = 0.01        # 1% minimum price difference
block_check_delay_ms = 100            # Block checking frequency
```

#### Pilot Fish Scanner (MEV Following)

```toml
[scanners.pilot_fish_scanner]
min_whale_trade_usd = 50000.0         # Minimum whale trade size
min_price_impact_usd = 5000.0         # Minimum price impact
min_expected_profit_usd = 10.0        # Minimum expected profit
profit_multiplier = 0.1               # Profit calculation multiplier
default_volatility = 0.02             # Default volatility assumption
```

## Risk Management

Comprehensive risk management is critical for autonomous trading operations.

### Core Risk Parameters

```toml
[risk]
max_daily_loss_usd = 1000.0           # Maximum daily loss limit
max_position_size_usd = 10000.0       # Maximum single position size
kelly_fraction_config = 0.25          # Kelly Criterion fraction (≤ 1.0)
max_consecutive_failures = 5          # Circuit breaker threshold
max_slippage = 0.005                  # Maximum slippage tolerance (0.5%)
max_gas_price = 100000000000          # Maximum gas price (100 gwei)
```

### Regime-Based Risk Multipliers

The system adapts risk parameters based on market conditions:

```toml
[risk.regime_multipliers]
# High volatility: Reduce position sizes and losses
high_volatility_position_mult = 0.5   # 50% of normal position size
high_volatility_loss_mult = 0.25      # 25% of normal loss limit

# Bot gas wars: Significantly reduce exposure
bot_gas_war_position_mult = 0.1       # 10% of normal position size
bot_gas_war_loss_mult = 0.1           # 10% of normal loss limit

# Retail FOMO: Increase exposure cautiously
retail_fomo_position_mult = 1.2       # 120% of normal position size
retail_fomo_loss_mult = 1.5           # 150% of normal loss limit

# Calm/orderly markets: Normal parameters
calm_orderly_position_mult = 1.0      # 100% of normal position size
calm_orderly_loss_mult = 1.0          # 100% of normal loss limit
```

### Kelly Criterion Implementation

The system uses the Kelly Criterion for optimal position sizing:

- **Formula**: f = (bp - q) / b
- **f**: Fraction of capital to risk
- **b**: Odds received (profit/loss ratio)
- **p**: Probability of winning
- **q**: Probability of losing (1-p)

```toml
[risk]
kelly_fraction_config = 0.25          # Conservative Kelly fraction
# Never risk more than 25% of capital on any single trade
```

## Execution Parameters

Fine-tune transaction execution for optimal performance and cost efficiency.

### Gas Strategy Configuration

```toml
[execution]
gas_limit = 500000                    # Default gas limit
max_priority_fee = 2000000000         # 2 gwei maximum priority fee
default_slippage_tolerance = 0.005    # 0.5% default slippage
fallback_base_fee_gwei = 10.0         # Fallback base fee
min_net_profit_threshold = 5.0        # Minimum profit after gas costs
```

### Dynamic Gas Multipliers

```toml
[execution]
# Market condition multipliers
gas_multiplier_network_shock = 1.5    # Network congestion
gas_multiplier_high_sp_time = 1.2     # High-speed trading periods
gas_multiplier_bot_gas_war = 2.0      # Bot competition
gas_multiplier_retail_fomo = 1.1      # Retail activity
gas_multiplier_high_volatility = 1.05 # Market volatility
network_shock_threshold_ms = 500      # Network shock detection threshold
```

### Gas Buffer Multipliers

```toml
[execution.gas_buffer_multipliers]
low = 1.1                             # 10% buffer for low priority
medium = 1.2                          # 20% buffer for medium priority
high = 1.3                            # 30% buffer for high priority
critical = 1.5                        # 50% buffer for critical trades
```

### Priority Fee Strategy

```toml
[execution.priority_fee_gwei]
low_gwei = 1.0                        # Low priority transactions
medium_gwei = 2.0                     # Medium priority transactions
high_gwei = 5.0                       # High priority transactions
critical_gwei = 10.0                  # Critical/time-sensitive transactions
```

## Infrastructure Configuration

Configure the supporting infrastructure services for optimal performance.

### Database Configuration

```toml
[database]
url = "postgres://basilisk:password@localhost:5432/basilisk_db"
# Use environment variable: DATABASE_URL for production
```

**Production Setup:**

```bash
# Create production database
createdb basilisk_production
psql basilisk_production < scripts/init_schema.sql

# Set environment variable
export DATABASE_URL="***********************************/basilisk_production"
```

### Redis Configuration

```toml
[redis]
url = "redis://localhost:6379"
# Use environment variable: REDIS_URL for production
```

**Production Setup:**

```bash
# Configure Redis for production
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# Set environment variable
export REDIS_URL="redis://prod-redis-host:6379"
```

### NATS Messaging Configuration

```toml
[nats]
url = "nats://localhost:4222"
subjects = [
    "market.>",                       # All market data
    "network.blocks",                 # Block notifications
    "gas.prices",                     # Gas price updates
    "are.temporal_harmonics",         # Chronos Sieve analysis
    "are.network_seismology",         # Network timing
    "are.geometric_score",            # Mandorla Gauge scoring
    "are.fractal_analysis",           # Fractal analysis
    "execution.trades",               # Trade execution
    "risk.alerts"                     # Risk alerts
]
queue_group = "basilisk_ingestors"
max_reconnect_attempts = 10
reconnect_delay_ms = 1000
```

**Production NATS Cluster:**

```bash
# Start NATS cluster
nats-server --cluster nats://0.0.0.0:6222 --port 4222 --http_port 8222
nats-server --cluster nats://0.0.0.0:6223 --port 4223 --routes nats://node1:6222

# Set environment variable
export NATS_URL="nats://nats-cluster:4222"
```

## Security Configuration

Implement comprehensive security measures for production deployment.

### Private Key Management

```bash
# NEVER store private keys in configuration files
# Always use environment variables
export BASILISK_EXECUTION_PRIVATE_KEY=your_private_key_without_0x_prefix
```

### RPC Endpoint Security

```toml
# Use multiple RPC endpoints for redundancy and security
[[chains.8453.rpc_endpoints]]
url = "https://mainnet.base.org"
priority = 0

[[chains.8453.rpc_endpoints]]
url = "https://base.publicnode.com"
priority = 1

# Use API keys for rate limit protection
# Set via environment variables:
# BASE_RPC_API_KEY=your_api_key
```

### MEV Protection

```bash
# Configure MEV protection relays
export FLASHBOTS_RELAY_URL=https://relay.flashbots.net
export TITAN_RELAY_URL=https://rpc.titanbuilder.xyz
```

### Honeypot Detection

```bash
# Configure honeypot detection service
export HONEYPOT_API_KEY=your_goplus_api_key
```

### Operational Security

```toml
# Authorized operators for system access
authorized_operators = ["production_operator", "backup_operator"]

# Enable dry run for testing
dry_run = true  # Set to false only after thorough testing
```

This comprehensive configuration guide covers all aspects of the Zen Geometer system configuration. For additional help, use the built-in validation and help commands or refer to the production deployment guide.
