// Unit Tests for Mathematical Functions
// Tests FractalAnalyzer, decimal operations, and core mathematical logic

use basilisk_bot::data::fractal_analyzer::FractalAnalyzer;
use basilisk_bot::math::{DecimalExt, optimized_sqrt};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use proptest::prelude::*;
use pretty_assertions::assert_eq;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sqrt_precision() {
        let analyzer = FractalAnalyzer::new();
        let test_cases = vec![
            (dec!(4), dec!(2)),
            (dec!(9), dec!(3)),
            (dec!(16), dec!(4)),
            (dec!(25), dec!(5)),
            (dec!(2), dec!(1.41421356237)),
            (dec!(0.5), dec!(0.70710678118)),
        ];

        for (input, expected) in test_cases {
            let result = analyzer.sqrt(input);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(0.00001),
                "sqrt({}) failed. Result: {}, Expected: {}, Diff: {}",
                input,
                result,
                expected,
                difference
            );
        }
    }

    #[test]
    fn test_natural_log_precision() {
        let analyzer = FractalAnalyzer::new();
        let test_cases = vec![
            (dec!(1), dec!(0)),
            (dec!(2), dec!(0.69314718056)),
            (dec!(0.5), dec!(-0.69314718056)),
            (dec!(1.1), dec!(0.0953101798)),
        ];

        for (input, expected) in test_cases {
            let result = analyzer.natural_log(input);
            let difference = (result - expected).abs();
            assert!(
                difference < dec!(0.0001),
                "natural_log({}) failed. Result: {}, Expected: {}, Diff: {}",
                input,
                result,
                expected,
                difference
            );
        }
    }

    #[test]
    fn test_decimal_edge_cases() {
        let analyzer = FractalAnalyzer::new();
        
        // Test zero handling
        assert_eq!(analyzer.sqrt(dec!(0)), dec!(0));
        
        // Test very small numbers
        let small_result = analyzer.sqrt(dec!(0.000001));
        assert!(small_result > dec!(0));
        assert!(small_result < dec!(0.01));
        
        // Test very large numbers
        let large_result = analyzer.sqrt(dec!(1000000));
        assert_eq!(large_result, dec!(1000));
    }

    #[test]
    fn test_mathematical_invariants() {
        let analyzer = FractalAnalyzer::new();

        // Test sqrt(x^2) = x for positive x
        let test_values = vec![dec!(1), dec!(2.5), dec!(10), dec!(100.75)];
        for x in test_values {
            let squared = x * x;
            let sqrt_result = analyzer.sqrt(squared);
            let difference = (sqrt_result - x).abs();
            assert!(
                difference < dec!(0.00001),
                "sqrt({}^2) != {}, got {}",
                x,
                x,
                sqrt_result
            );
        }
    }

    #[test]
    fn test_decimal_ext_sqrt() {
        // Test DecimalExt sqrt implementation
        let test_cases = vec![
            (dec!(0), Some(dec!(0))),
            (dec!(1), Some(dec!(1))),
            (dec!(4), Some(dec!(2))),
            (dec!(9), Some(dec!(3))),
            (dec!(16), Some(dec!(4))),
            (dec!(25), Some(dec!(5))),
        ];

        for (input, expected) in test_cases {
            let result = input.sqrt();
            match (result, expected) {
                (Some(actual), Some(exp)) => {
                    let difference = (actual - exp).abs();
                    assert!(
                        difference < dec!(0.00001),
                        "sqrt({}) failed. Result: {:?}, Expected: {:?}",
                        input, actual, exp
                    );
                }
                (None, None) => {} // Both None is correct
                _ => panic!("Mismatch: sqrt({}) = {:?}, expected {:?}", input, result, expected),
            }
        }

        // Test negative input
        assert_eq!(dec!(-1).sqrt(), None);
        assert_eq!(dec!(-100).sqrt(), None);
    }

    #[test]
    fn test_optimized_sqrt_edge_cases() {
        // Test the optimized sqrt function directly
        assert_eq!(optimized_sqrt(dec!(0)), dec!(0));
        assert_eq!(optimized_sqrt(dec!(1)), dec!(1));

        // Test very small numbers
        let small_result = optimized_sqrt(dec!(0.000001));
        assert!(small_result > dec!(0));
        assert!(small_result < dec!(0.01));

        // Test very large numbers
        let large_result = optimized_sqrt(dec!(1000000));
        let expected = dec!(1000);
        let difference = (large_result - expected).abs();
        assert!(difference < dec!(0.00001));

        // Test precision with known values
        let sqrt_2 = optimized_sqrt(dec!(2));
        let expected_sqrt_2 = dec!(1.41421356237);
        let difference = (sqrt_2 - expected_sqrt_2).abs();
        assert!(difference < dec!(0.00001));
    }

    #[test]
    fn test_decimal_precision_limits() {
        let analyzer = FractalAnalyzer::new();

        // Test maximum precision
        let max_decimal = Decimal::MAX;
        let sqrt_max = analyzer.sqrt(max_decimal);
        assert!(sqrt_max > dec!(0));

        // Test minimum positive value
        let min_positive = Decimal::new(1, 28); // Smallest positive decimal
        let sqrt_min = analyzer.sqrt(min_positive);
        assert!(sqrt_min > dec!(0));
        assert!(sqrt_min < dec!(1));
    }

    #[test]
    fn test_natural_log_edge_cases() {
        let analyzer = FractalAnalyzer::new();

        // Test ln(1) = 0
        assert_eq!(analyzer.natural_log(dec!(1)), dec!(0));

        // Test ln(e) ≈ 1 (where e ≈ 2.718281828)
        let e_approx = dec!(2.718281828);
        let ln_e = analyzer.natural_log(e_approx);
        let difference = (ln_e - dec!(1)).abs();
        assert!(difference < dec!(0.001));

        // Test ln(1/e) ≈ -1
        let inv_e = dec!(1) / e_approx;
        let ln_inv_e = analyzer.natural_log(inv_e);
        let difference = (ln_inv_e - dec!(-1)).abs();
        assert!(difference < dec!(0.001));
    }

    #[test]
    fn test_mathematical_properties() {
        let analyzer = FractalAnalyzer::new();

        // Test ln(a*b) = ln(a) + ln(b)
        let a = dec!(2);
        let b = dec!(3);
        let ln_a = analyzer.natural_log(a);
        let ln_b = analyzer.natural_log(b);
        let ln_ab = analyzer.natural_log(a * b);
        let sum_ln = ln_a + ln_b;
        let difference = (ln_ab - sum_ln).abs();
        assert!(difference < dec!(0.001), "ln(a*b) != ln(a) + ln(b)");

        // Test sqrt(a*b) = sqrt(a) * sqrt(b)
        let sqrt_a = analyzer.sqrt(a);
        let sqrt_b = analyzer.sqrt(b);
        let sqrt_ab = analyzer.sqrt(a * b);
        let product_sqrt = sqrt_a * sqrt_b;
        let difference = (sqrt_ab - product_sqrt).abs();
        assert!(difference < dec!(0.00001), "sqrt(a*b) != sqrt(a) * sqrt(b)");
    }
}

// Property-based tests for mathematical functions
proptest! {
    #[test]
    fn test_sqrt_properties(
        x in 0.0..1000000.0_f64
    ) {
        let decimal_x = Decimal::from_f64(x).unwrap();
        let analyzer = FractalAnalyzer::new();
        let sqrt_result = analyzer.sqrt(decimal_x);

        // sqrt should always be non-negative
        prop_assert!(sqrt_result >= Decimal::ZERO);

        // sqrt(x^2) should equal x (within tolerance)
        let squared = sqrt_result * sqrt_result;
        let difference = (squared - decimal_x).abs();
        prop_assert!(difference < dec!(0.001));
    }

    #[test]
    fn test_natural_log_properties(
        x in 0.1..1000.0_f64
    ) {
        let decimal_x = Decimal::from_f64(x).unwrap();
        let analyzer = FractalAnalyzer::new();
        let ln_result = analyzer.natural_log(decimal_x);

        // ln(1) should be 0
        if (decimal_x - dec!(1)).abs() < dec!(0.0001) {
            prop_assert!((ln_result - dec!(0)).abs() < dec!(0.001));
        }

        // ln should be monotonic: if x > y, then ln(x) > ln(y)
        let y = decimal_x / dec!(2);
        let ln_y = analyzer.natural_log(y);
        if decimal_x > y && y > dec!(0) {
            prop_assert!(ln_result > ln_y);
        }
    }

    #[test]
    fn test_decimal_ext_properties(
        x in 0.0..10000.0_f64
    ) {
        let decimal_x = Decimal::from_f64(x).unwrap();

        // Test sqrt properties
        if let Some(sqrt_x) = decimal_x.sqrt() {
            prop_assert!(sqrt_x >= Decimal::ZERO);

            // Test that sqrt(x)^2 ≈ x
            let squared = sqrt_x * sqrt_x;
            let difference = (squared - decimal_x).abs();
            prop_assert!(difference < dec!(0.01));
        }
    }
}