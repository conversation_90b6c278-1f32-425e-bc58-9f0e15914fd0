# Basilisk Bot Development Environment
# Includes development tools, hot reloading, and debugging capabilities

FROM rust:1.87

# Install development dependencies including faster linkers
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    cmake \
    git \
    vim \
    htop \
    curl \
    wget \
    clang \
    lld \
    && rm -rf /var/lib/apt/lists/*

# Install cargo tools for development
RUN cargo install cargo-watch cargo-edit cargo-audit cargo-tarpaulin

# Create non-root user for development
RUN useradd -m -u 1000 basilisk_bot

# Set the working directory
WORKDIR /workspace

# Change ownership to development user
RUN chown -R basilisk_bot:basilisk_bot /workspace

# Switch to development user
USER basilisk_bot

# Set environment variables for development
ENV RUST_LOG=debug
ENV RUST_BACKTRACE=1
ENV ENVIRONMENT=development

# Default command for development (can be overridden)
CMD ["cargo", "run", "--", "run", "--dry-run"]
