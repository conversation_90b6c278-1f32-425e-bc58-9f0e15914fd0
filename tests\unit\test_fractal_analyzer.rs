// Unit Tests for Fractal Analyzer and Chronos Sieve
// Tests temporal analysis, market regime detection, and fractal calculations

use basilisk_bot::data::fractal_analyzer::{FractalAnalyzer, PricePoint, KalmanState};
use basilisk_bot::shared_types::{MarketCharacter, MarketRegime, TokenPair};
use chrono::{DateTime, Utc, TimeZone};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use proptest::prelude::*;
use pretty_assertions::assert_eq;
use std::collections::VecDeque;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_fractal_analyzer_creation() {
        let analyzer = FractalAnalyzer::new();
        
        // Verify initial state
        assert!(analyzer.price_history_1m.is_empty());
        assert!(analyzer.price_history_15m.is_empty());
        assert!(analyzer.price_history_1h.is_empty());
        
        // Verify default configuration
        assert_eq!(analyzer.max_history_1m, 60);  // 1 hour of 1-minute data
        assert_eq!(analyzer.max_history_15m, 96); // 24 hours of 15-minute data
        assert_eq!(analyzer.max_history_1h, 168); // 1 week of hourly data
    }

    #[test]
    fn test_price_point_creation() {
        let price = dec!(100.50);
        let timestamp = Utc::now();
        
        let point = PricePoint { price, timestamp };
        
        assert_eq!(point.price, price);
        assert_eq!(point.timestamp, timestamp);
    }

    #[test]
    fn test_kalman_state_initialization() {
        let state = KalmanState {
            state_estimate: dec!(100.0),
            error_covariance: dec!(1.0),
            process_noise: dec!(0.01),
            measurement_noise: dec!(0.05),
        };
        
        assert_eq!(state.state_estimate, dec!(100.0));
        assert_eq!(state.error_covariance, dec!(1.0));
        assert_eq!(state.process_noise, dec!(0.01));
        assert_eq!(state.measurement_noise, dec!(0.05));
    }

    #[tokio::test]
    async fn test_add_price_point() {
        let mut analyzer = FractalAnalyzer::new();
        let price = dec!(100.0);
        let timestamp = Utc::now();
        
        let result = analyzer.add_price_point(price, timestamp);
        assert!(result.is_ok());
        
        // Check that price was added to 1-minute history
        assert_eq!(analyzer.price_history_1m.len(), 1);
        assert_eq!(analyzer.price_history_1m[0].timestamp, timestamp);
    }

    #[tokio::test]
    async fn test_add_multiple_price_points() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc.with_ymd_and_hms(2024, 1, 1, 12, 0, 0).unwrap();
        
        // Add multiple price points
        for i in 0..10 {
            let price = dec!(100.0) + Decimal::from(i);
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            
            let result = analyzer.add_price_point(price, timestamp);
            assert!(result.is_ok());
        }
        
        assert_eq!(analyzer.price_history_1m.len(), 10);
        
        // Verify prices are in correct order
        for (i, point) in analyzer.price_history_1m.iter().enumerate() {
            let expected_price = dec!(100.0) + Decimal::from(i);
            // Allow for small differences due to Kalman filtering
            let difference = (point.price - expected_price).abs();
            assert!(difference < dec!(1.0), "Price mismatch at index {}", i);
        }
    }

    #[test]
    fn test_history_size_limits() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add more than max_history_1m points
        for i in 0..100 {
            let price = dec!(100.0);
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        // Should not exceed max_history_1m
        assert!(analyzer.price_history_1m.len() <= analyzer.max_history_1m);
    }

    #[test]
    fn test_market_character_classification() {
        let analyzer = FractalAnalyzer::new();
        
        // Test different Hurst exponent ranges
        assert_eq!(
            analyzer.classify_market_character(dec!(0.3)),
            MarketCharacter::MeanReverting
        );
        assert_eq!(
            analyzer.classify_market_character(dec!(0.5)),
            MarketCharacter::RandomWalk
        );
        assert_eq!(
            analyzer.classify_market_character(dec!(0.7)),
            MarketCharacter::Trending
        );
        assert_eq!(
            analyzer.classify_market_character(dec!(0.9)),
            MarketCharacter::Trending
        );
    }

    #[test]
    fn test_hurst_exponent_calculation() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add trending price data
        for i in 0..50 {
            let price = dec!(100.0) + Decimal::from(i); // Trending upward
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        let hurst = analyzer.calculate_hurst_exponent(&analyzer.price_history_1m);
        
        // Trending data should have Hurst > 0.5
        assert!(hurst > dec!(0.5), "Trending data should have Hurst > 0.5, got {}", hurst);
        assert!(hurst <= dec!(1.0), "Hurst exponent should be <= 1.0, got {}", hurst);
    }

    #[test]
    fn test_volatility_calculation() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add stable price data (low volatility)
        for i in 0..20 {
            let price = dec!(100.0) + dec!(0.01) * Decimal::from(i % 2); // Small oscillation
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        let volatility = analyzer.calculate_volatility(&analyzer.price_history_1m);
        
        // Should be low volatility
        assert!(volatility < dec!(1.0), "Low volatility expected, got {}", volatility);
        assert!(volatility >= dec!(0.0), "Volatility should be non-negative");
    }

    #[test]
    fn test_returns_calculation() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add price data with known returns
        let prices = vec![dec!(100.0), dec!(105.0), dec!(110.0), dec!(115.0)];
        for (i, &price) in prices.iter().enumerate() {
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        let returns = analyzer.calculate_returns(&analyzer.price_history_1m);
        assert!(returns.is_ok());
        
        let returns_value = returns.unwrap();
        // Should be positive returns for upward trending prices
        assert!(returns_value > dec!(0.0), "Expected positive returns, got {}", returns_value);
    }

    #[tokio::test]
    async fn test_kalman_filter() {
        let mut analyzer = FractalAnalyzer::new();
        
        // Test with noisy data
        let noisy_prices = vec![
            dec!(100.0), dec!(102.0), dec!(98.0), dec!(101.0), dec!(99.0)
        ];
        
        let mut filtered_prices = Vec::new();
        for price in noisy_prices {
            let filtered = analyzer.apply_kalman_filter(price);
            assert!(filtered.is_ok());
            filtered_prices.push(filtered.unwrap());
        }
        
        // Filtered prices should be smoother than raw prices
        assert_eq!(filtered_prices.len(), 5);
        
        // First price should be unchanged (initialization)
        assert_eq!(filtered_prices[0], dec!(100.0));
        
        // Subsequent prices should be smoothed
        for price in &filtered_prices[1..] {
            assert!(*price > dec!(95.0) && *price < dec!(105.0));
        }
    }

    #[tokio::test]
    async fn test_market_phase_detection() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add sufficient data for analysis
        for i in 0..20 {
            let price = dec!(100.0) + Decimal::from(i);
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        let token_pair = TokenPair {
            token_a: "WETH".to_string(),
            token_b: "USDC".to_string(),
        };
        
        let phase = analyzer.get_market_phase(&token_pair).await;
        assert!(phase.is_ok());
    }

    #[test]
    fn test_insufficient_data_handling() {
        let analyzer = FractalAnalyzer::new();
        
        // Test with empty data
        let volatility = analyzer.calculate_volatility(&VecDeque::new());
        assert_eq!(volatility, dec!(0.5)); // Default volatility
        
        let hurst = analyzer.calculate_hurst_exponent(&VecDeque::new());
        assert_eq!(hurst, dec!(0.5)); // Default Hurst exponent
    }

    #[test]
    fn test_edge_case_single_price() {
        let mut analyzer = FractalAnalyzer::new();
        let timestamp = Utc::now();
        
        let _ = analyzer.add_price_point(dec!(100.0), timestamp);
        
        // Should handle single price point gracefully
        let volatility = analyzer.calculate_volatility(&analyzer.price_history_1m);
        assert!(volatility >= dec!(0.0));
        
        let hurst = analyzer.calculate_hurst_exponent(&analyzer.price_history_1m);
        assert!(hurst >= dec!(0.0) && hurst <= dec!(1.0));
    }

    #[test]
    fn test_temporal_harmonics_insufficient_data() {
        let analyzer = FractalAnalyzer::new();
        
        // Test with insufficient data (less than 60 points)
        let (cycles, stability) = analyzer.calculate_temporal_harmonics();
        
        assert!(cycles.is_empty());
        assert_eq!(stability, 0.0);
    }

    #[test]
    fn test_wavelet_features_calculation() {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add some price data
        for i in 0..20 {
            let price = dec!(100.0) + dec!(0.1) * Decimal::from(i);
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(price, timestamp);
        }
        
        let features = analyzer.calculate_wavelet_features(&analyzer.price_history_1m);
        
        // Should return some features
        assert!(!features.is_empty());
        
        // All features should be non-negative (energy values)
        for feature in features {
            assert!(feature >= Decimal::ZERO);
        }
    }
}

// Property-based tests for FractalAnalyzer
proptest! {
    #[test]
    fn test_price_point_properties(
        price in 0.01..10000.0_f64,
        timestamp_offset in 0..86400i64 // 24 hours in seconds
    ) {
        let decimal_price = Decimal::from_f64(price).unwrap();
        let timestamp = Utc::now() + chrono::Duration::seconds(timestamp_offset);
        
        let point = PricePoint { price: decimal_price, timestamp };
        
        prop_assert_eq!(point.price, decimal_price);
        prop_assert_eq!(point.timestamp, timestamp);
    }

    #[test]
    fn test_hurst_exponent_bounds(
        prices in prop::collection::vec(1.0..1000.0_f64, 10..100)
    ) {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add price data
        for (i, &price) in prices.iter().enumerate() {
            let decimal_price = Decimal::from_f64(price).unwrap();
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(decimal_price, timestamp);
        }
        
        let hurst = analyzer.calculate_hurst_exponent(&analyzer.price_history_1m);
        
        // Hurst exponent should be between 0 and 1
        prop_assert!(hurst >= Decimal::ZERO);
        prop_assert!(hurst <= Decimal::ONE);
    }

    #[test]
    fn test_volatility_properties(
        prices in prop::collection::vec(1.0..1000.0_f64, 5..50)
    ) {
        let mut analyzer = FractalAnalyzer::new();
        let base_time = Utc::now();
        
        // Add price data
        for (i, &price) in prices.iter().enumerate() {
            let decimal_price = Decimal::from_f64(price).unwrap();
            let timestamp = base_time + chrono::Duration::minutes(i as i64);
            let _ = analyzer.add_price_point(decimal_price, timestamp);
        }
        
        let volatility = analyzer.calculate_volatility(&analyzer.price_history_1m);
        
        // Volatility should be non-negative
        prop_assert!(volatility >= Decimal::ZERO);
    }

    #[test]
    fn test_kalman_filter_properties(
        raw_price in 1.0..1000.0_f64
    ) {
        let mut analyzer = FractalAnalyzer::new();
        let decimal_price = Decimal::from_f64(raw_price).unwrap();
        
        let filtered = analyzer.apply_kalman_filter(decimal_price);
        prop_assert!(filtered.is_ok());
        
        let filtered_price = filtered.unwrap();
        
        // Filtered price should be reasonable
        prop_assert!(filtered_price > Decimal::ZERO);
        prop_assert!(filtered_price < dec!(1000000)); // Reasonable upper bound
    }
}
