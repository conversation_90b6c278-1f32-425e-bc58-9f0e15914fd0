// Unit Tests for Shared Types and Data Structures
// Tests serialization, validation, conversions, and business logic

use basilisk_bot::shared_types::*;
use ethers::types::{Address, H256, U256};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde_json;
use proptest::prelude::*;
use pretty_assertions::assert_eq;
use std::collections::HashMap;
use std::str::FromStr;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_run_mode_serialization() {
        let modes = vec![
            RunMode::Simulate,
            RunMode::Shadow,
            RunMode::Sentinel,
            RunMode::Live,
        ];

        for mode in modes {
            let serialized = serde_json::to_string(&mode).unwrap();
            let deserialized: RunMode = serde_json::from_str(&serialized).unwrap();
            assert_eq!(mode, deserialized);
        }
    }

    #[test]
    fn test_geometric_score_creation() {
        let score = GeometricScore {
            convexity_ratio: dec!(0.75),
            liquidity_centroid_bias: dec!(0.60),
            harmonic_path_score: dec!(0.85),
            vesica_piscis_depth: dec!(0.90),
        };

        assert_eq!(score.convexity_ratio, dec!(0.75));
        assert_eq!(score.liquidity_centroid_bias, dec!(0.60));
        assert_eq!(score.harmonic_path_score, dec!(0.85));
        assert_eq!(score.vesica_piscis_depth, dec!(0.90));
    }

    #[test]
    fn test_geometric_score_bounds() {
        let score = GeometricScore {
            convexity_ratio: dec!(0.5),
            liquidity_centroid_bias: dec!(0.5),
            harmonic_path_score: dec!(0.5),
            vesica_piscis_depth: dec!(0.5),
        };

        // All scores should be between 0 and 1
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.convexity_ratio <= Decimal::ONE);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias <= Decimal::ONE);
        assert!(score.harmonic_path_score >= Decimal::ZERO);
        assert!(score.harmonic_path_score <= Decimal::ONE);
        assert!(score.vesica_piscis_depth >= Decimal::ZERO);
        assert!(score.vesica_piscis_depth <= Decimal::ONE);
    }

    #[test]
    fn test_geometric_score_serialization() {
        let score = GeometricScore {
            convexity_ratio: dec!(0.75),
            liquidity_centroid_bias: dec!(0.60),
            harmonic_path_score: dec!(0.85),
            vesica_piscis_depth: dec!(0.90),
        };

        let serialized = serde_json::to_string(&score).unwrap();
        let deserialized: GeometricScore = serde_json::from_str(&serialized).unwrap();
        
        assert_eq!(score.convexity_ratio, deserialized.convexity_ratio);
        assert_eq!(score.liquidity_centroid_bias, deserialized.liquidity_centroid_bias);
        assert_eq!(score.harmonic_path_score, deserialized.harmonic_path_score);
        assert_eq!(score.vesica_piscis_depth, deserialized.vesica_piscis_depth);
    }

    #[test]
    fn test_network_resonance_state() {
        let state = NetworkResonanceState {
            sp_time_ms: 15.5,
            network_coherence_score: 0.85,
            is_shock_event: false,
            sp_time_20th_percentile: 12.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        };

        assert_eq!(state.sp_time_ms, 15.5);
        assert_eq!(state.network_coherence_score, 0.85);
        assert!(!state.is_shock_event);
        assert_eq!(state.sp_time_20th_percentile, 12.0);
        assert_eq!(state.sequencer_status, "Healthy");
        assert!(!state.censorship_detected);
    }

    #[test]
    fn test_temporal_harmonics() {
        let harmonics = TemporalHarmonics {
            dominant_cycles_minutes: vec![(15.0, 0.8), (30.0, 0.6), (60.0, 0.4)],
            market_rhythm_stability: 0.75,
        };

        assert_eq!(harmonics.dominant_cycles_minutes.len(), 3);
        assert_eq!(harmonics.market_rhythm_stability, 0.75);
        
        // Verify cycle data
        assert_eq!(harmonics.dominant_cycles_minutes[0], (15.0, 0.8));
        assert_eq!(harmonics.dominant_cycles_minutes[1], (30.0, 0.6));
        assert_eq!(harmonics.dominant_cycles_minutes[2], (60.0, 0.4));
    }

    #[test]
    fn test_market_regime_enum() {
        let regimes = vec![
            MarketRegime::CalmOrderly,
            MarketRegime::RetailFomoSpike,
            MarketRegime::BotGasWar,
            MarketRegime::HighVolatilityCorrection,
            MarketRegime::Trending,
            MarketRegime::Unknown,
        ];

        for regime in regimes {
            let serialized = serde_json::to_string(&regime).unwrap();
            let deserialized: MarketRegime = serde_json::from_str(&serialized).unwrap();
            assert_eq!(regime, deserialized);
        }
    }

    #[test]
    fn test_market_character_enum() {
        let characters = vec![
            MarketCharacter::Trending,
            MarketCharacter::MeanReverting,
            MarketCharacter::RandomWalk,
        ];

        for character in characters {
            let serialized = serde_json::to_string(&character).unwrap();
            let deserialized: MarketCharacter = serde_json::from_str(&serialized).unwrap();
            assert_eq!(character, deserialized);
        }
    }

    #[test]
    fn test_market_regime_state() {
        let state = MarketRegimeState {
            regime: MarketRegime::Trending,
            confidence: dec!(0.85),
            state_probabilities: vec![dec!(0.1), dec!(0.2), dec!(0.3), dec!(0.25), dec!(0.15)],
        };

        assert_eq!(state.regime, MarketRegime::Trending);
        assert_eq!(state.confidence, dec!(0.85));
        assert_eq!(state.state_probabilities.len(), 5);
        
        // Probabilities should sum to approximately 1.0
        let sum: Decimal = state.state_probabilities.iter().sum();
        assert!((sum - Decimal::ONE).abs() < dec!(0.01));
    }

    #[test]
    fn test_arbitrage_pool() {
        let pool = ArbitragePool {
            address: Address::from_str("******************************************").unwrap(),
            token0_symbol: "WETH".to_string(),
            token1_symbol: "USDC".to_string(),
            reserve0: dec!(1000.0),
            reserve1: dec!(2000000.0),
            protocol: "uniswap".to_string(),
        };

        assert_eq!(pool.token0_symbol, "WETH");
        assert_eq!(pool.token1_symbol, "USDC");
        assert_eq!(pool.reserve0, dec!(1000.0));
        assert_eq!(pool.reserve1, dec!(2000000.0));
        assert_eq!(pool.protocol, "uniswap");
    }

    #[test]
    fn test_arbitrage_path_type_alias() {
        let pool1 = ArbitragePool {
            address: Address::random(),
            token0_symbol: "WETH".to_string(),
            token1_symbol: "USDC".to_string(),
            reserve0: dec!(1000.0),
            reserve1: dec!(2000000.0),
            protocol: "uniswap".to_string(),
        };

        let pool2 = ArbitragePool {
            address: Address::random(),
            token0_symbol: "USDC".to_string(),
            token1_symbol: "DAI".to_string(),
            reserve0: dec!(2000000.0),
            reserve1: dec!(2000000.0),
            protocol: "sushiswap".to_string(),
        };

        let path: ArbitragePath = vec![pool1, pool2];
        assert_eq!(path.len(), 2);
    }

    #[test]
    fn test_protocol_type_enum() {
        let protocols = vec![
            ProtocolType::UniswapV2,
            ProtocolType::UniswapV3,
            ProtocolType::SushiSwap,
            ProtocolType::PancakeSwap,
            ProtocolType::Other("Custom".to_string()),
        ];

        for protocol in protocols {
            let serialized = serde_json::to_string(&protocol).unwrap();
            let deserialized: ProtocolType = serde_json::from_str(&serialized).unwrap();
            assert_eq!(protocol, deserialized);
        }
    }

    #[test]
    fn test_transaction_classification() {
        let classifications = vec![
            TransactionClassification::RetailUser,
            TransactionClassification::WhaleUser,
            TransactionClassification::ArbitrageBot,
            TransactionClassification::MevBot,
            TransactionClassification::LiquidationBot,
            TransactionClassification::Unknown,
        ];

        for classification in classifications {
            let serialized = serde_json::to_string(&classification).unwrap();
            let deserialized: TransactionClassification = serde_json::from_str(&serialized).unwrap();
            assert_eq!(classification, deserialized);
        }
    }

    #[test]
    fn test_congestion_level_enum() {
        let levels = vec![
            CongestionLevel::Low,
            CongestionLevel::Moderate,
            CongestionLevel::High,
            CongestionLevel::Extreme,
        ];

        for level in levels {
            let serialized = serde_json::to_string(&level).unwrap();
            let deserialized: CongestionLevel = serde_json::from_str(&serialized).unwrap();
            assert_eq!(level, deserialized);
        }
    }

    #[test]
    fn test_service_status_methods() {
        let running = ServiceStatus::Running;
        let error = ServiceStatus::Error("Test error".to_string());
        let warning = ServiceStatus::Warning("Test warning".to_string());

        assert!(running.is_healthy());
        assert!(!running.is_error());
        assert!(!running.is_warning());

        assert!(!error.is_healthy());
        assert!(error.is_error());
        assert!(!error.is_warning());

        assert!(!warning.is_healthy());
        assert!(!warning.is_error());
        assert!(warning.is_warning());
    }

    #[test]
    fn test_order_book_update_creation() {
        let bids = vec![
            OrderBookLevel { price: dec!(100.0), quantity: dec!(10.0) },
            OrderBookLevel { price: dec!(99.5), quantity: dec!(15.0) },
        ];
        let asks = vec![
            OrderBookLevel { price: dec!(100.5), quantity: dec!(12.0) },
            OrderBookLevel { price: dec!(101.0), quantity: dec!(8.0) },
        ];

        let update = OrderBookUpdate::new(
            "binance".to_string(),
            "ETHUSDC".to_string(),
            bids.clone(),
            asks.clone(),
        );

        assert_eq!(update.exchange, "binance");
        assert_eq!(update.pair, "ETHUSDC");
        assert_eq!(update.bids, bids);
        assert_eq!(update.asks, asks);
        assert!(update.timestamp > 0);
    }

    #[test]
    fn test_chain_block_structure() {
        let block = ChainBlock {
            chain_id: 1,
            block_number: 18000000,
            block_hash: H256::random(),
            timestamp: 1640995200,
            gas_used: U256::from(15000000),
            gas_limit: U256::from(30000000),
            base_fee_per_gas: Some(U256::from(20000000000u64)),
        };

        assert_eq!(block.chain_id, 1);
        assert_eq!(block.block_number, 18000000);
        assert_eq!(block.timestamp, 1640995200);
        assert_eq!(block.gas_used, U256::from(15000000));
        assert_eq!(block.gas_limit, U256::from(30000000));
        assert!(block.base_fee_per_gas.is_some());
    }

    #[test]
    fn test_token_pair() {
        let pair = TokenPair {
            token_a: "WETH".to_string(),
            token_b: "USDC".to_string(),
        };

        assert_eq!(pair.token_a, "WETH");
        assert_eq!(pair.token_b, "USDC");
    }

    #[test]
    fn test_risk_assessment() {
        let assessment = RiskAssessment {
            risk_score: dec!(0.3),
            max_position_size_usd: dec!(10000.0),
            estimated_slippage: dec!(0.005),
            confidence_interval: (dec!(0.25), dec!(0.35)),
        };

        assert_eq!(assessment.risk_score, dec!(0.3));
        assert_eq!(assessment.max_position_size_usd, dec!(10000.0));
        assert_eq!(assessment.estimated_slippage, dec!(0.005));
        assert_eq!(assessment.confidence_interval.0, dec!(0.25));
        assert_eq!(assessment.confidence_interval.1, dec!(0.35));
    }

    #[test]
    fn test_execution_priority_enum() {
        let priorities = vec![
            ExecutionPriority::Low,
            ExecutionPriority::Medium,
            ExecutionPriority::High,
            ExecutionPriority::Critical,
        ];

        for priority in priorities {
            let serialized = serde_json::to_string(&priority).unwrap();
            let deserialized: ExecutionPriority = serde_json::from_str(&serialized).unwrap();
            assert_eq!(priority, deserialized);
        }
    }

    #[test]
    fn test_strategy_type_enum() {
        let strategies = vec![
            StrategyType::DexDexArbitrage,
            StrategyType::CexDexArbitrage,
            StrategyType::Sandwich,
            StrategyType::Liquidation,
            StrategyType::ZenGeometer,
        ];

        for strategy in strategies {
            let serialized = serde_json::to_string(&strategy).unwrap();
            let deserialized: StrategyType = serde_json::from_str(&serialized).unwrap();
            assert_eq!(strategy, deserialized);
        }
    }
}

// Property-based tests for shared types
proptest! {
    #[test]
    fn test_geometric_score_properties(
        convexity in 0.0..1.0_f64,
        liquidity in 0.0..1.0_f64,
        harmonic in 0.0..1.0_f64,
        vesica in 0.0..1.0_f64
    ) {
        let score = GeometricScore {
            convexity_ratio: Decimal::from_f64(convexity).unwrap(),
            liquidity_centroid_bias: Decimal::from_f64(liquidity).unwrap(),
            harmonic_path_score: Decimal::from_f64(harmonic).unwrap(),
            vesica_piscis_depth: Decimal::from_f64(vesica).unwrap(),
        };

        // All scores should be between 0 and 1
        prop_assert!(score.convexity_ratio >= Decimal::ZERO);
        prop_assert!(score.convexity_ratio <= Decimal::ONE);
        prop_assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        prop_assert!(score.liquidity_centroid_bias <= Decimal::ONE);
        prop_assert!(score.harmonic_path_score >= Decimal::ZERO);
        prop_assert!(score.harmonic_path_score <= Decimal::ONE);
        prop_assert!(score.vesica_piscis_depth >= Decimal::ZERO);
        prop_assert!(score.vesica_piscis_depth <= Decimal::ONE);

        // Test serialization round-trip
        let serialized = serde_json::to_string(&score).unwrap();
        let deserialized: GeometricScore = serde_json::from_str(&serialized).unwrap();
        prop_assert_eq!(score.convexity_ratio, deserialized.convexity_ratio);
    }

    #[test]
    fn test_network_resonance_properties(
        sp_time in 0.0..1000.0_f64,
        coherence in 0.0..1.0_f64,
        percentile in 0.0..1000.0_f64
    ) {
        let state = NetworkResonanceState {
            sp_time_ms: sp_time,
            network_coherence_score: coherence,
            is_shock_event: sp_time > 100.0,
            sp_time_20th_percentile: percentile,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        };

        prop_assert!(state.sp_time_ms >= 0.0);
        prop_assert!(state.network_coherence_score >= 0.0);
        prop_assert!(state.network_coherence_score <= 1.0);
        prop_assert!(state.sp_time_20th_percentile >= 0.0);

        // Test serialization
        let serialized = serde_json::to_string(&state).unwrap();
        let deserialized: NetworkResonanceState = serde_json::from_str(&serialized).unwrap();
        prop_assert_eq!(state.sp_time_ms, deserialized.sp_time_ms);
    }

    #[test]
    fn test_arbitrage_pool_properties(
        reserve0 in 1.0..1000000.0_f64,
        reserve1 in 1.0..1000000.0_f64
    ) {
        let pool = ArbitragePool {
            address: Address::random(),
            token0_symbol: "TOKEN0".to_string(),
            token1_symbol: "TOKEN1".to_string(),
            reserve0: Decimal::from_f64(reserve0).unwrap(),
            reserve1: Decimal::from_f64(reserve1).unwrap(),
            protocol: "test".to_string(),
        };

        prop_assert!(pool.reserve0 > Decimal::ZERO);
        prop_assert!(pool.reserve1 > Decimal::ZERO);
        prop_assert!(!pool.token0_symbol.is_empty());
        prop_assert!(!pool.token1_symbol.is_empty());
        prop_assert!(!pool.protocol.is_empty());
    }
}
