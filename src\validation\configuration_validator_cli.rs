// src/validation/configuration_validator_cli.rs

//! CLI interface for configuration and infrastructure validation
//! 
//! This module provides command-line tools for running configuration validation
//! tests, including parameter validation, network connectivity, database connections,
//! NATS messaging, contract addresses, and security parameters.

use crate::config::Config;
use crate::validation::configuration_validator::{
    ConfigurationValidator, SecurityValidationConfig, ConfigurationValidationMetrics
};
use crate::validation::{ValidationFrameworkResult, ValidationStatus};
use clap::{Args, Subcommand};
use std::time::Duration;
use tracing::{info, warn, error};

/// Configuration validation CLI arguments
#[derive(Debug, Args)]
pub struct ConfigurationValidationArgs {
    #[command(subcommand)]
    pub command: ConfigurationValidationCommand,
}

/// Configuration validation commands
#[derive(Debug, Subcommand)]
pub enum ConfigurationValidationCommand {
    /// Run comprehensive configuration validation
    Comprehensive {
        /// Skip network connectivity tests
        #[arg(long)]
        offline: bool,
        /// Network timeout in seconds
        #[arg(long, default_value = "10")]
        timeout: u64,
        /// Skip security validation
        #[arg(long)]
        skip_security: bool,
    },
    /// Validate only configuration parameters
    Parameters,
    /// Validate only network endpoints
    Network {
        /// Skip actual connectivity tests
        #[arg(long)]
        offline: bool,
        /// Network timeout in seconds
        #[arg(long, default_value = "10")]
        timeout: u64,
    },
    /// Validate only database connections
    Database {
        /// Skip actual connectivity tests
        #[arg(long)]
        offline: bool,
    },
    /// Validate only NATS messaging
    Nats {
        /// Skip actual connectivity tests
        #[arg(long)]
        offline: bool,
    },
    /// Validate only contract addresses
    Contracts,
    /// Validate only security parameters
    Security {
        /// Skip private key validation
        #[arg(long)]
        skip_private_keys: bool,
        /// Skip production key checks
        #[arg(long)]
        skip_production_checks: bool,
    },
    /// Run quick validation demo
    Demo,
}

/// Handle configuration validation command
pub async fn handle_configuration_validation_command(
    args: ConfigurationValidationArgs,
) -> ValidationFrameworkResult<()> {
    match args.command {
        ConfigurationValidationCommand::Comprehensive { offline, timeout, skip_security } => {
            run_comprehensive_validation(offline, timeout, skip_security).await
        }
        ConfigurationValidationCommand::Parameters => {
            run_parameter_validation().await
        }
        ConfigurationValidationCommand::Network { offline, timeout } => {
            run_network_validation(offline, timeout).await
        }
        ConfigurationValidationCommand::Database { offline } => {
            run_database_validation(offline).await
        }
        ConfigurationValidationCommand::Nats { offline } => {
            run_nats_validation(offline).await
        }
        ConfigurationValidationCommand::Contracts => {
            run_contract_validation().await
        }
        ConfigurationValidationCommand::Security { skip_private_keys, skip_production_checks } => {
            run_security_validation(skip_private_keys, skip_production_checks).await
        }
        ConfigurationValidationCommand::Demo => {
            run_configuration_validation_demo().await
        }
    }
}

/// Run comprehensive configuration validation
async fn run_comprehensive_validation(
    offline: bool,
    timeout_secs: u64,
    skip_security: bool,
) -> ValidationFrameworkResult<()> {
    println!("🔧 Running Comprehensive Configuration Validation");
    println!("================================================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let security_config = if skip_security {
        SecurityValidationConfig {
            validate_private_keys: false,
            check_production_keys: false,
            min_key_entropy: 0,
        }
    } else {
        SecurityValidationConfig::default()
    };

    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(timeout_secs),
        !offline,
        security_config,
    );

    let result = validator.validate_comprehensive().await?;
    
    display_comprehensive_results(&result.metrics);
    
    match result.status {
        ValidationStatus::Passed => {
            println!("\n✅ All configuration validation checks passed!");
            Ok(())
        }
        ValidationStatus::Warning => {
            println!("\n⚠️  Configuration validation completed with warnings");
            for warning in &result.warnings {
                warn!("Warning: {:?}", warning);
            }
            Ok(())
        }
        ValidationStatus::Failed => {
            println!("\n❌ Configuration validation failed!");
            for error in &result.errors {
                error!("Error: {:?}", error);
            }
            Err(crate::error::BasiliskError::SerializeError { message: "Configuration validation failed".to_string() })
        }
        _ => Ok(()),
    }
}

/// Display comprehensive validation results
fn display_comprehensive_results(metrics: &ConfigurationValidationMetrics) {
    println!("\n📊 Validation Results Summary");
    println!("============================");
    println!("Overall Status: {} {:?}", metrics.overall_status.to_color_code(), metrics.overall_status);
    println!("Total Validation Time: {}ms", metrics.total_validation_time_ms);
    println!("Timestamp: {}", metrics.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));

    // Parameter validation results
    println!("\n📋 Parameter Validation");
    println!("Parameters Validated: {}", metrics.parameter_validation.parameters_validated);
    println!("Validation Errors: {}", metrics.parameter_validation.validation_errors);
    println!("Warnings: {}", metrics.parameter_validation.warnings);
    
    for (param_name, result) in &metrics.parameter_validation.parameter_results {
        println!("  {} {}: {} - {}", 
            result.status.to_color_code(),
            param_name,
            result.current_value,
            result.message
        );
    }

    // Network validation results
    println!("\n🌐 Network Validation");
    println!("Endpoints Tested: {}", metrics.network_validation.endpoints_tested);
    println!("Successful Connections: {}", metrics.network_validation.successful_connections);
    println!("Failed Connections: {}", metrics.network_validation.failed_connections);
    println!("Average Response Time: {:.1}ms", metrics.network_validation.average_response_time_ms);
    
    for (chain_id, failover_result) in &metrics.network_validation.failover_test_results {
        println!("  Chain {}: {} Primary: {} ({:?}ms)", 
            chain_id,
            if failover_result.failover_successful { "✅" } else { "❌" },
            if failover_result.primary_status.connected { "✅" } else { "❌" },
            failover_result.primary_status.response_time_ms
        );
        
        for (i, backup) in failover_result.backup_endpoints.iter().enumerate() {
            println!("    Backup {}: {} ({:?}ms)", 
                i + 1,
                if backup.connected { "✅" } else { "❌" },
                backup.response_time_ms
            );
        }
    }

    // Database validation results
    println!("\n🗄️  Database Validation");
    println!("PostgreSQL: {} ({}ms)", 
        if metrics.database_validation.postgres_status.connected { "✅" } else { "❌" },
        metrics.database_validation.postgres_status.connection_time_ms.unwrap_or(0)
    );
    println!("Redis: {} ({}ms)", 
        if metrics.database_validation.redis_status.connected { "✅" } else { "❌" },
        metrics.database_validation.redis_status.connection_time_ms.unwrap_or(0)
    );
    println!("Connection Pool: {}", 
        if metrics.database_validation.connection_pool_status.configuration_valid { "✅" } else { "❌" }
    );
    println!("SSL/TLS: {}", 
        if metrics.database_validation.ssl_validation.ssl_enabled { "✅" } else { "⚠️" }
    );

    // NATS validation results
    println!("\n📨 NATS Validation");
    println!("Connection: {} ({}ms)", 
        if metrics.nats_validation.connection_status.connected { "✅" } else { "❌" },
        metrics.nats_validation.connection_status.connection_time_ms.unwrap_or(0)
    );
    println!("TLS Enabled: {}", 
        if metrics.nats_validation.connection_status.tls_enabled { "✅" } else { "⚠️" }
    );
    println!("Authenticated: {}", 
        if metrics.nats_validation.connection_status.authenticated { "✅" } else { "⚠️" }
    );
    
    if !metrics.nats_validation.subscription_tests.is_empty() {
        println!("Subscription Tests:");
        for (subject, result) in &metrics.nats_validation.subscription_tests {
            println!("  {}: {}", subject, 
                if result.subscription_successful { "✅" } else { "❌" }
            );
        }
    }

    // Contract validation results
    println!("\n📜 Contract Validation");
    println!("Overall Status: {} {:?}", 
        metrics.contract_validation.overall_status.to_color_code(),
        metrics.contract_validation.overall_status
    );
    
    for (contract_name, validation) in &metrics.contract_validation.address_validation {
        println!("  {}: {} Format: {} Exists: {} Verified: {}", 
            contract_name,
            if validation.address_format_valid { "✅" } else { "❌" },
            if validation.address_format_valid { "✅" } else { "❌" },
            if validation.contract_exists { "✅" } else { "❌" },
            if validation.bytecode_verified { "✅" } else { "❌" }
        );
    }

    // Security validation results
    println!("\n🔒 Security Validation");
    println!("Overall Status: {} {:?}", 
        metrics.security_validation.overall_security_status.to_color_code(),
        metrics.security_validation.overall_security_status
    );
    
    println!("Private Keys Validated: {}", metrics.security_validation.private_key_validation.len());
    for (key_name, validation) in &metrics.security_validation.private_key_validation {
        println!("  {}: {} Security Level: {:?}", 
            key_name,
            if validation.format_valid { "✅" } else { "❌" },
            validation.security_level
        );
    }
    
    println!("API Keys Validated: {}", metrics.security_validation.api_key_validation.len());
    for (service_name, validation) in &metrics.security_validation.api_key_validation {
        println!("  {}: {} Length: {}", 
            service_name,
            if validation.format_valid { "✅" } else { "❌" },
            if validation.length_adequate { "✅" } else { "⚠️" }
        );
    }

    println!("Environment Security: {}", 
        if metrics.security_validation.environment_security.security_requirements_met { "✅" } else { "❌" }
    );
}

/// Run parameter validation only
async fn run_parameter_validation() -> ValidationFrameworkResult<()> {
    println!("📋 Running Parameter Validation");
    println!("===============================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let validator = ConfigurationValidator::new(config);
    let metrics = validator.validate_parameters().await?;
    
    println!("Parameters Validated: {}", metrics.parameters_validated);
    println!("Validation Errors: {}", metrics.validation_errors);
    println!("Warnings: {}", metrics.warnings);
    
    for (param_name, result) in &metrics.parameter_results {
        println!("\n{} Parameter: {}", result.status.to_color_code(), param_name);
        println!("  Current Value: {}", result.current_value);
        println!("  Expected Format: {}", result.expected_format);
        println!("  Message: {}", result.message);
    }
    
    if metrics.validation_errors > 0 {
        return Err(crate::error::BasiliskError::SerializeError { message: "Parameter validation failed".to_string() });
    }
    
    println!("\n✅ Parameter validation completed successfully!");
    Ok(())
}

/// Run network validation only
async fn run_network_validation(offline: bool, timeout_secs: u64) -> ValidationFrameworkResult<()> {
    println!("🌐 Running Network Validation");
    println!("=============================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(timeout_secs),
        !offline,
        SecurityValidationConfig::default(),
    );

    let metrics = if offline {
        validator.validate_network_endpoints_offline().await?
    } else {
        validator.validate_network_endpoints().await?
    };
    
    println!("Endpoints Tested: {}", metrics.endpoints_tested);
    println!("Successful Connections: {}", metrics.successful_connections);
    println!("Failed Connections: {}", metrics.failed_connections);
    
    if !offline {
        println!("Average Response Time: {:.1}ms", metrics.average_response_time_ms);
    }
    
    for (chain_id, failover_result) in &metrics.failover_test_results {
        println!("\nChain {}: {}", chain_id, failover_result.chain_id);
        println!("  Primary: {} {} ({:?}ms)", 
            if failover_result.primary_status.connected { "✅" } else { "❌" },
            failover_result.primary_status.url,
            failover_result.primary_status.response_time_ms
        );
        
        if let Some(ref error) = failover_result.primary_status.error_message {
            println!("    Error: {}", error);
        }
        
        for (i, backup) in failover_result.backup_endpoints.iter().enumerate() {
            println!("  Backup {}: {} {} ({:?}ms)", 
                i + 1,
                if backup.connected { "✅" } else { "❌" },
                backup.url,
                backup.response_time_ms
            );
            
            if let Some(ref error) = backup.error_message {
                println!("    Error: {}", error);
            }
        }
        
        println!("  Failover Successful: {}", 
            if failover_result.failover_successful { "✅" } else { "❌" }
        );
    }
    
    if metrics.failed_connections > 0 && metrics.successful_connections == 0 {
        return Err(crate::error::BasiliskError::SerializeError { message: "All network endpoints failed".to_string() });
    }
    
    println!("\n✅ Network validation completed!");
    Ok(())
}

/// Run database validation only
async fn run_database_validation(offline: bool) -> ValidationFrameworkResult<()> {
    println!("🗄️  Running Database Validation");
    println!("===============================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(10),
        !offline,
        SecurityValidationConfig::default(),
    );

    let metrics = if offline {
        validator.validate_database_configuration().await?
    } else {
        validator.validate_database_connections().await?
    };
    
    println!("PostgreSQL/TimescaleDB:");
    println!("  Connected: {}", if metrics.postgres_status.connected { "✅" } else { "❌" });
    if let Some(time) = metrics.postgres_status.connection_time_ms {
        println!("  Connection Time: {}ms", time);
    }
    if let Some(ref version) = metrics.postgres_status.version {
        println!("  Version: {}", version);
    }
    if let Some(ref error) = metrics.postgres_status.error_message {
        println!("  Error: {}", error);
    }
    
    println!("\nRedis:");
    println!("  Connected: {}", if metrics.redis_status.connected { "✅" } else { "❌" });
    if let Some(time) = metrics.redis_status.connection_time_ms {
        println!("  Connection Time: {}ms", time);
    }
    if let Some(ref error) = metrics.redis_status.error_message {
        println!("  Error: {}", error);
    }
    
    println!("\nConnection Pool:");
    println!("  Configuration Valid: {}", 
        if metrics.connection_pool_status.configuration_valid { "✅" } else { "❌" }
    );
    println!("  Load Capacity Adequate: {}", 
        if metrics.connection_pool_status.load_capacity_adequate { "✅" } else { "❌" }
    );
    println!("  Timeout Settings Valid: {}", 
        if metrics.connection_pool_status.timeout_settings_valid { "✅" } else { "❌" }
    );
    
    for message in &metrics.connection_pool_status.messages {
        println!("  - {}", message);
    }
    
    println!("\nSSL/TLS:");
    println!("  SSL Enabled: {}", if metrics.ssl_validation.ssl_enabled { "✅" } else { "⚠️" });
    println!("  Certificate Valid: {}", if metrics.ssl_validation.certificate_valid { "✅" } else { "❌" });
    println!("  Encryption Adequate: {}", if metrics.ssl_validation.encryption_adequate { "✅" } else { "❌" });
    
    for message in &metrics.ssl_validation.messages {
        println!("  - {}", message);
    }
    
    if !metrics.postgres_status.connected && !metrics.redis_status.connected {
        return Err(crate::error::BasiliskError::SerializeError { message: "Database validation failed".to_string() });
    }
    
    println!("\n✅ Database validation completed!");
    Ok(())
}

/// Run NATS validation only
async fn run_nats_validation(offline: bool) -> ValidationFrameworkResult<()> {
    println!("📨 Running NATS Validation");
    println!("==========================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(10),
        !offline,
        SecurityValidationConfig::default(),
    );

    let metrics = if offline {
        validator.validate_nats_configuration().await?
    } else {
        validator.validate_nats_messaging().await?
    };
    
    println!("Connection:");
    println!("  Connected: {}", if metrics.connection_status.connected { "✅" } else { "❌" });
    if let Some(time) = metrics.connection_status.connection_time_ms {
        println!("  Connection Time: {}ms", time);
    }
    println!("  TLS Enabled: {}", if metrics.connection_status.tls_enabled { "✅" } else { "⚠️" });
    println!("  Authenticated: {}", if metrics.connection_status.authenticated { "✅" } else { "⚠️" });
    if let Some(ref error) = metrics.connection_status.error_message {
        println!("  Error: {}", error);
    }
    
    if !metrics.subscription_tests.is_empty() {
        println!("\nSubscription Tests:");
        for (subject, result) in &metrics.subscription_tests {
            println!("  {}: {}", subject, if result.subscription_successful { "✅" } else { "❌" });
            if let Some(time) = result.response_time_ms {
                println!("    Response Time: {}ms", time);
            }
            if let Some(ref error) = result.error_message {
                println!("    Error: {}", error);
            }
        }
    }
    
    if !metrics.publishing_tests.is_empty() {
        println!("\nPublishing Tests:");
        for (subject, result) in &metrics.publishing_tests {
            println!("  {}: {}", subject, if result.publishing_successful { "✅" } else { "❌" });
            if let Some(time) = result.publish_time_ms {
                println!("    Publish Time: {}ms", time);
            }
            if let Some(ref error) = result.error_message {
                println!("    Error: {}", error);
            }
        }
    }
    
    println!("\nSecurity:");
    println!("  TLS Valid: {}", if metrics.security_validation.tls_valid { "✅" } else { "⚠️" });
    println!("  Auth Configured: {}", if metrics.security_validation.auth_configured { "✅" } else { "⚠️" });
    println!("  Permissions Valid: {}", if metrics.security_validation.permissions_valid { "✅" } else { "❌" });
    
    for message in &metrics.security_validation.messages {
        println!("  - {}", message);
    }
    
    if !metrics.connection_status.connected {
        warn!("NATS connection failed - this may impact real-time functionality");
    }
    
    println!("\n✅ NATS validation completed!");
    Ok(())
}

/// Run contract validation only
async fn run_contract_validation() -> ValidationFrameworkResult<()> {
    println!("📜 Running Contract Validation");
    println!("==============================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let validator = ConfigurationValidator::new(config);
    let metrics = validator.validate_contract_addresses().await?;
    
    println!("Overall Status: {} {:?}", 
        metrics.overall_status.to_color_code(),
        metrics.overall_status
    );
    
    println!("\nAddress Validation:");
    for (contract_name, validation) in &metrics.address_validation {
        println!("  {}:", contract_name);
        println!("    Format Valid: {}", if validation.address_format_valid { "✅" } else { "❌" });
        println!("    Contract Exists: {}", if validation.contract_exists { "✅" } else { "❌" });
        println!("    Bytecode Verified: {}", if validation.bytecode_verified { "✅" } else { "❌" });
        println!("    Address Matches Expected: {}", if validation.address_matches_expected { "✅" } else { "❌" });
        
        for message in &validation.messages {
            println!("      - {}", message);
        }
    }
    
    if !metrics.abi_consistency.is_empty() {
        println!("\nABI Consistency:");
        for (contract_name, consistency) in &metrics.abi_consistency {
            println!("  {}:", contract_name);
            println!("    ABI Loaded: {}", if consistency.abi_loaded { "✅" } else { "❌" });
            println!("    Function Signatures Match: {}", if consistency.function_signatures_match { "✅" } else { "❌" });
            println!("    Event Signatures Match: {}", if consistency.event_signatures_match { "✅" } else { "❌" });
            println!("    Version Compatible: {}", if consistency.version_compatible { "✅" } else { "❌" });
            
            for message in &consistency.messages {
                println!("      - {}", message);
            }
        }
    }
    
    if !metrics.deployment_verification.is_empty() {
        println!("\nDeployment Verification:");
        for (contract_name, verification) in &metrics.deployment_verification {
            println!("  {}:", contract_name);
            println!("    Deployment Confirmed: {}", if verification.deployment_confirmed { "✅" } else { "❌" });
            if let Some(block) = verification.deployment_block {
                println!("    Deployment Block: {}", block);
            }
            if let Some(ref deployer) = verification.deployer_address {
                println!("    Deployer: {}", deployer);
            }
            println!("    Explorer Verified: {}", if verification.explorer_verified { "✅" } else { "❌" });
            
            for message in &verification.messages {
                println!("      - {}", message);
            }
        }
    }
    
    if metrics.overall_status == ValidationStatus::Failed {
        return Err(crate::error::BasiliskError::SerializeError { message: "Contract validation failed".to_string() });
    }
    
    println!("\n✅ Contract validation completed!");
    Ok(())
}

/// Run security validation only
async fn run_security_validation(
    skip_private_keys: bool,
    skip_production_checks: bool,
) -> ValidationFrameworkResult<()> {
    println!("🔒 Running Security Validation");
    println!("==============================");
    
    let config = Config::load().map_err(|e| {
        crate::error::BasiliskError::ConfigError(format!("Failed to load config: {}", e))
    })?;

    let security_config = SecurityValidationConfig {
        validate_private_keys: !skip_private_keys,
        check_production_keys: !skip_production_checks,
        min_key_entropy: 128,
    };

    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(10),
        false,
        security_config,
    );

    let metrics = validator.validate_security_parameters().await?;
    
    println!("Overall Security Status: {} {:?}", 
        metrics.overall_security_status.to_color_code(),
        metrics.overall_security_status
    );
    
    if !skip_private_keys {
        println!("\nPrivate Key Validation:");
        for (key_name, validation) in &metrics.private_key_validation {
            println!("  {}:", key_name);
            println!("    Format Valid: {}", if validation.format_valid { "✅" } else { "❌" });
            println!("    Entropy Adequate: {}", if validation.entropy_adequate { "✅" } else { "⚠️" });
            println!("    Not Test Key: {}", if validation.not_test_key { "✅" } else { "❌" });
            println!("    Derivation Valid: {}", if validation.derivation_valid { "✅" } else { "❌" });
            println!("    Security Level: {:?}", validation.security_level);
            
            for message in &validation.messages {
                if message.contains("WARNING") || message.contains("DO NOT USE") {
                    println!("      ⚠️  {}", message);
                } else {
                    println!("      - {}", message);
                }
            }
        }
    }
    
    println!("\nAPI Key Validation:");
    for (service_name, validation) in &metrics.api_key_validation {
        println!("  {}:", service_name);
        println!("    Format Valid: {}", if validation.format_valid { "✅" } else { "❌" });
        println!("    Length Adequate: {}", if validation.length_adequate { "✅" } else { "⚠️" });
        println!("    Appears Active: {}", if validation.appears_active { "✅" } else { "❓" });
        
        for message in &validation.messages {
            println!("      - {}", message);
        }
    }
    
    println!("\nEnvironment Security:");
    println!("  Environment Type: {}", metrics.environment_security.environment_type);
    println!("  Security Requirements Met: {}", 
        if metrics.environment_security.security_requirements_met { "✅" } else { "❌" }
    );
    println!("  Sensitive Data Protected: {}", 
        if metrics.environment_security.sensitive_data_protected { "✅" } else { "❌" }
    );
    println!("  Access Controls Valid: {}", 
        if metrics.environment_security.access_controls_valid { "✅" } else { "❌" }
    );
    
    for message in &metrics.environment_security.messages {
        println!("    - {}", message);
    }
    
    if metrics.overall_security_status == ValidationStatus::Failed {
        return Err(crate::error::BasiliskError::SerializeError { message: "Security validation failed".to_string() });
    }
    
    println!("\n✅ Security validation completed!");
    Ok(())
}

/// Run configuration validation demo
pub async fn run_configuration_validation_demo() -> ValidationFrameworkResult<()> {
    println!("🎯 Configuration Validation Demo");
    println!("================================");
    println!("This demo showcases the configuration and infrastructure validation capabilities.");
    println!();

    // Load configuration
    println!("📋 Loading configuration...");
    let config = match Config::load() {
        Ok(config) => {
            println!("✅ Configuration loaded successfully");
            config
        }
        Err(e) => {
            println!("❌ Failed to load configuration: {}", e);
            println!("💡 Make sure config files exist and are properly formatted");
            return Err(crate::error::BasiliskError::ConfigError(format!("Config load failed: {}", e)));
        }
    };

    // Create validator
    let validator = ConfigurationValidator::new(config);

    // Demo 1: Parameter validation
    println!("\n🔍 Demo 1: Parameter Validation");
    println!("-------------------------------");
    match validator.validate_parameters().await {
        Ok(metrics) => {
            println!("✅ Parameter validation completed");
            println!("   Parameters validated: {}", metrics.parameters_validated);
            println!("   Errors: {}", metrics.validation_errors);
            println!("   Warnings: {}", metrics.warnings);
            
            // Show a few example results
            let mut count = 0;
            for (param_name, result) in &metrics.parameter_results {
                if count < 3 {
                    println!("   {} {}: {}", result.status.to_color_code(), param_name, result.message);
                    count += 1;
                }
            }
            if metrics.parameter_results.len() > 3 {
                println!("   ... and {} more parameters", metrics.parameter_results.len() - 3);
            }
        }
        Err(e) => {
            println!("❌ Parameter validation failed: {}", e);
        }
    }

    // Demo 2: Network validation (offline mode for demo)
    println!("\n🌐 Demo 2: Network Validation (Offline Mode)");
    println!("--------------------------------------------");
    match validator.validate_network_endpoints_offline().await {
        Ok(metrics) => {
            println!("✅ Network validation completed");
            println!("   Endpoints tested: {}", metrics.endpoints_tested);
            println!("   Successful: {}", metrics.successful_connections);
            println!("   Failed: {}", metrics.failed_connections);
            
            // Show first chain result
            if let Some((chain_id, result)) = metrics.failover_test_results.iter().next() {
                println!("   Chain {}: {} (Primary: {})", 
                    chain_id,
                    if result.failover_successful { "✅" } else { "❌" },
                    result.primary_status.url
                );
            }
        }
        Err(e) => {
            println!("❌ Network validation failed: {}", e);
        }
    }

    // Demo 3: Contract validation
    println!("\n📜 Demo 3: Contract Validation");
    println!("------------------------------");
    match validator.validate_contract_addresses().await {
        Ok(metrics) => {
            println!("✅ Contract validation completed");
            println!("   Overall status: {:?}", metrics.overall_status);
            println!("   Addresses validated: {}", metrics.address_validation.len());
            println!("   ABI checks: {}", metrics.abi_consistency.len());
            
            // Show first contract result
            if let Some((contract_name, validation)) = metrics.address_validation.iter().next() {
                println!("   {}: {} (Format: {}, Exists: {})", 
                    contract_name,
                    if validation.address_format_valid { "✅" } else { "❌" },
                    if validation.address_format_valid { "✅" } else { "❌" },
                    if validation.contract_exists { "✅" } else { "❌" }
                );
            }
        }
        Err(e) => {
            println!("❌ Contract validation failed: {}", e);
        }
    }

    // Demo 4: Security validation (limited for demo)
    println!("\n🔒 Demo 4: Security Validation");
    println!("------------------------------");
    let security_config = SecurityValidationConfig {
        validate_private_keys: false, // Skip for demo
        check_production_keys: false,
        min_key_entropy: 64,
    };
    
    let demo_validator = ConfigurationValidator::with_settings(
        validator.config.clone(),
        Duration::from_secs(5),
        false,
        security_config,
    );

    match demo_validator.validate_security_parameters().await {
        Ok(metrics) => {
            println!("✅ Security validation completed");
            println!("   Overall status: {:?}", metrics.overall_security_status);
            println!("   Environment: {}", metrics.environment_security.environment_type);
            println!("   Security requirements met: {}", 
                if metrics.environment_security.security_requirements_met { "✅" } else { "❌" }
            );
        }
        Err(e) => {
            println!("❌ Security validation failed: {}", e);
        }
    }

    println!("\n🎉 Configuration Validation Demo Complete!");
    println!("==========================================");
    println!("💡 Usage Examples:");
    println!("   cargo run -- validation config comprehensive");
    println!("   cargo run -- validation config parameters");
    println!("   cargo run -- validation config network --offline");
    println!("   cargo run -- validation config security --skip-private-keys");
    println!();
    println!("📚 This validation framework ensures your trading system has:");
    println!("   • Correct configuration parameters");
    println!("   • Working network connectivity");
    println!("   • Valid database connections");
    println!("   • Functional NATS messaging");
    println!("   • Verified contract addresses");
    println!("   • Secure key management");

    Ok(())
}

/// Show configuration validation usage examples
pub fn show_configuration_validation_usage_examples() {
    println!("🔧 Configuration Validation Usage Examples");
    println!("==========================================");
    println!();
    
    println!("📋 Basic Commands:");
    println!("  cargo run -- validation config comprehensive");
    println!("  cargo run -- validation config parameters");
    println!("  cargo run -- validation config network");
    println!("  cargo run -- validation config database");
    println!("  cargo run -- validation config nats");
    println!("  cargo run -- validation config contracts");
    println!("  cargo run -- validation config security");
    println!();
    
    println!("🌐 Network Validation Options:");
    println!("  cargo run -- validation config network --offline");
    println!("  cargo run -- validation config network --timeout 30");
    println!("  cargo run -- validation config comprehensive --offline");
    println!();
    
    println!("🔒 Security Validation Options:");
    println!("  cargo run -- validation config security --skip-private-keys");
    println!("  cargo run -- validation config security --skip-production-checks");
    println!("  cargo run -- validation config comprehensive --skip-security");
    println!();
    
    println!("🗄️  Database Validation Options:");
    println!("  cargo run -- validation config database --offline");
    println!("  cargo run -- validation config nats --offline");
    println!();
    
    println!("🎯 Demo and Examples:");
    println!("  cargo run -- validation config demo");
    println!();
    
    println!("💡 Environment Variables:");
    println!("  DATABASE_URL=postgresql://localhost:5432/basilisk");
    println!("  REDIS_URL=redis://localhost:6379");
    println!("  DATABASE_MAX_CONNECTIONS=20");
    println!("  DATABASE_CONNECTION_TIMEOUT=30");
    println!("  DATABASE_SSL_MODE=require");
    println!("  NATS_USER=username");
    println!("  NATS_TOKEN=token");
    println!();
    
    println!("📊 What Gets Validated:");
    println!("  ✅ Configuration parameter ranges and formats");
    println!("  ✅ Network endpoint connectivity and failover");
    println!("  ✅ Database connection pools and SSL settings");
    println!("  ✅ NATS messaging security and subscriptions");
    println!("  ✅ Smart contract addresses and ABI consistency");
    println!("  ✅ Private key security and environment protection");
}