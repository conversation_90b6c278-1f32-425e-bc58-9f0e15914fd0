use basilisk_bot::cli::{Cli, Commands};
use basilisk_bot::config::Config;
use basilisk_bot::logging::{init_structured_logging, HeartbeatLogger, TradingContext};
use basilisk_bot::{log_info, log_error};
use clap::Parser;
use std::sync::Arc;
use tracing::{error, info, warn};
use anyhow::Context;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize structured JSON logging
    if let Err(e) = init_structured_logging() {
        eprintln!("Failed to initialize structured logging: {}", e);
        // Fallback to basic logging
        tracing_subscriber::fmt::init();
    }

    // Create main context for application lifecycle
    let main_context = TradingContext::new("Main", "main");

    // Parse command line arguments
    let args = Cli::parse();

    // Handle mathematical validation early (doesn't need config)
    if let Commands::Validate { math: Some(model), .. } = &args.command {
        info!("ZEN GEOMETER: Starting mathematical validation");
        if let Err(e) = basilisk_bot::cli_handlers::validate_mathematical_models(Some(model)).await {
            error!("Mathematical validation failed: {}", e);
            return Err(format!("Mathematical validation error: {}", e).into());
        }
        return Ok(());
    }

    // Load configuration using the new elegant system
    info!("Loading configuration using elegant layered system...");

    // Set CONFIG_PATH if provided via CLI
    if let Some(config_path) = &args.config {
        std::env::set_var("CONFIG_PATH", config_path);
    }

    // Load and validate configuration with new system
    let new_config = Config::load()?;
    new_config.validate().context("Configuration validation failed")?;

    // Keep the new config for the new system
    let config = Arc::new(new_config.clone());

    log_info!(main_context, "ZEN GEOMETER: System initialization complete with elegant configuration");

    // Demonstrate new configuration capabilities
    info!("✅ Configuration loaded with {} chains configured", config.chains.len());
    for (chain_id, chain_config) in &config.chains {
        info!("🔗 Chain {}: {} ({})", chain_id, chain_config.name,
              chain_config.rpc_endpoints.as_ref().and_then(|eps| eps.first()).map(|e| &e.url).unwrap_or(&"no RPC".to_string()));
    }

    // Show configuration details from new system
    info!("📊 Strategy: Kelly fraction cap = {}", new_config.strategy.kelly_fraction_cap);
    info!("⚡ Execution: Max slippage = {}bps", new_config.execution.max_slippage_bps);
    info!("🎯 Enabled strategies: {:?}", new_config.strategy.enabled_strategies);

    // Show that environment variables can override configuration
    if let Ok(kelly_override) = std::env::var("APP_STRATEGY__KELLY_FRACTION_CAP") {
        info!("🌍 Kelly fraction cap overridden via environment: {}", kelly_override);
    }

    // Initialize heartbeat logger for system health monitoring
    let heartbeat_logger = Arc::new(HeartbeatLogger::new());
    
    // Handle commands
    match args.command {
        Commands::Tui => {
            let tui_context = main_context.clone().with_strategy("TUI");
            log_info!(tui_context, "ZEN GEOMETER: Starting TUI interface");
            basilisk_bot::cli_handlers::handle_tui_command(config).await?;
        }
        Commands::Run { mode, verbose } => {
            let run_context = main_context.clone().with_strategy(&format!("{:?}", mode));
            log_info!(run_context, "ZEN GEOMETER: Starting in {} mode", mode);
            if verbose {
                log_info!(run_context, "VERBOSE: Detailed logging enabled");
            }

            match mode {
                basilisk_bot::shared_types::RunMode::Simulate => {
                    info!("SIMULATION MODE: Educational trading with live data analysis");
                    info!("SAFE LEARNING: All transactions intercepted - no real money at risk");
                    
                    // Launch real TUI with simulation data
                    if let Err(e) = launch_real_tui_simulation(&config).await {
                        error!("TUI failed: {}", e);
                        return Err(format!("TUI error: {}", e).into());
                    }
                }
                basilisk_bot::shared_types::RunMode::Shadow => {
                    info!("SHADOW MODE: Live simulation with on-chain verification");
                    
                    if let Err(e) = basilisk_bot::operational_modes::run_shadow_mode(&config, verbose).await {
                        error!("Shadow mode failed: {}", e);
                        return Err(format!("Shadow mode error: {}", e).into());
                    }
                }
                basilisk_bot::shared_types::RunMode::Sentinel => {
                    info!("SENTINEL MODE: Live monitoring with minimal capital");
                    
                    if let Err(e) = basilisk_bot::operational_modes::run_sentinel_mode(&config, verbose).await {
                        error!("Sentinel mode failed: {}", e);
                        return Err(format!("Sentinel mode error: {}", e).into());
                    }
                }
                basilisk_bot::shared_types::RunMode::LowCapital => {
                    info!("LOW-CAPITAL MODE: Live trading with conservative limits");
                    
                    if let Err(e) = basilisk_bot::operational_modes::run_low_capital_mode(&config, verbose).await {
                        error!("Low-capital mode failed: {}", e);
                        return Err(format!("Low-capital mode error: {}", e).into());
                    }
                }
                basilisk_bot::shared_types::RunMode::Live => {
                    info!("LIVE MODE: Full production trading");
                    
                    if let Err(e) = basilisk_bot::operational_modes::run_live_mode(&config, verbose).await {
                        error!("Live mode failed: {}", e);
                        return Err(format!("Live mode error: {}", e).into());
                    }
                }
            }
        }
        Commands::Config { command } => {
            info!("Config command: {:?}", command);
            // Simplified config handling
        }
        Commands::Validate { command: None, reason, math: None } => {
            let validate_context = main_context.clone().with_strategy("Validate");
            info!("ZEN GEOMETER: Starting system validation");
            
            // System validation
            if let Err(e) = basilisk_bot::cli_handlers::validate_system(&config).await {
                error!("System validation failed: {}", e);
                return Err(format!("System validation error: {}", e).into());
            }
            
            if reason {
                info!("Detailed validation reasoning enabled");
                // Add detailed reasoning output here
            }
        }
        _ => {
            info!("Command not implemented yet");
        }
    }

    info!("ZEN GEOMETER: Operation completed successfully");
    Ok(())
}

async fn launch_real_tui_simulation(config: &Arc<Config>) -> Result<(), Box<dyn std::error::Error>> {
    use basilisk_bot::tui::app::App;
    use crossterm::{
        event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
        execute,
        terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
    };
    use ratatui::{backend::CrosstermBackend, Terminal};
    use std::io;

    info!("Starting REAL TUI Dashboard in Simulation Mode");

    // Connect to NATS (optional for simulation)
    let nats_client = match async_nats::connect(&config.nats.url).await {
        Ok(client) => {
            info!("Connected to NATS at {}", config.nats.url);
            client
        }
        Err(_) => {
            info!("NATS not available, using offline simulation mode");
            // For simulation, we can continue without NATS
            return launch_offline_tui_simulation().await;
        }
    };

    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create mock components for simulation
    let execution_config = Arc::new(config.execution.clone());
    // Create price oracle with mock provider and empty feed addresses
    let mock_provider = Arc::new(ethers::providers::Provider::try_from("http://localhost:8545")?);
    let price_oracle = Arc::new(basilisk_bot::data::price_oracle::PriceOracle::new(mock_provider, std::collections::HashMap::new()));

    // Create the real App instance with simulation data
    let mut app = App::new(config.clone(), nats_client, price_oracle);
    
    // Initialize with realistic simulation data
    // Network seismology data will be populated by the update loop

    // Set system metrics (using correct field names)
    app.system_metrics.active_strategies = 3;
    app.system_metrics.total_trades_24h = 127;
    app.system_metrics.total_pnl_24h = rust_decimal::Decimal::new(4500, 2); // $45.00

    let mut running = true;
    let mut tick_counter = 0u64;

    info!("TUI Dashboard launched! Press [N] for Network Seismology, [Q] to quit");

    while running {
        // Update the app with fresh simulation data
        if tick_counter % 50 == 0 {
            app.tick_counter = tick_counter;
            if let Err(e) = app.update().await {
                error!("App update failed: {}", e);
            }
        }

        // Render the REAL TUI
        terminal.draw(|f| {
            app.render(f);
        })?;

        // Handle input
        if event::poll(std::time::Duration::from_millis(100))? {
            if let Event::Key(key) = event::read()? {
                match key.code {
                    KeyCode::Char('q') | KeyCode::Char('Q') => running = false,
                    _ => {
                        app.handle_key(key);
                    }
                }
            }
        }

        tick_counter += 1;
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

    // Cleanup
    disable_raw_mode()?;
    execute!(terminal.backend_mut(), LeaveAlternateScreen, DisableMouseCapture)?;
    terminal.show_cursor()?;

    info!("Real TUI Dashboard simulation complete");
    Ok(())
}

async fn launch_offline_tui_simulation() -> Result<(), Box<dyn std::error::Error>> {
    use crossterm::{
        event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
        execute,
        terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
    };
    use ratatui::{
        backend::CrosstermBackend,
        layout::{Constraint, Direction, Layout},
        style::{Color, Modifier, Style},
        text::{Line, Span},
        widgets::{Block, Borders, Paragraph},
        Terminal,
    };
    use std::io;

    info!("Starting Offline TUI Simulation (NATS not available)");

    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    let mut running = true;
    let mut current_tab = 0;
    let tabs = vec!["Dashboard", "Network", "Strategies", "Help"];

    while running {
        terminal.draw(|f| {
            let size = f.size();
            
            let chunks = Layout::default()
                .direction(Direction::Vertical)
                .constraints([Constraint::Length(3), Constraint::Min(0)])
                .split(size);

            // Tab bar
            let tab_text = tabs.iter()
                .enumerate()
                .map(|(i, &title)| {
                    if i == current_tab {
                        format!("[{}] ", title)
                    } else {
                        format!("{} ", title)
                    }
                })
                .collect::<String>();

            let tab_paragraph = Paragraph::new(Line::from(Span::styled(
                tab_text,
                Style::default().fg(Color::Yellow)
            )))
            .block(Block::default().borders(Borders::ALL).title("Zen Geometer - Simulation Mode"));
            f.render_widget(tab_paragraph, chunks[0]);

            // Main content
            let content_lines = match current_tab {
                0 => vec![
                    Line::from(Span::styled("SIMULATION MODE ACTIVE", Style::default().fg(Color::Green).add_modifier(Modifier::BOLD))),
                    Line::from(""),
                    Line::from("System Status: Educational Mode"),
                    Line::from("Network: Base Mainnet (Simulated)"),
                    Line::from("Safety: No real money at risk"),
                    Line::from("NATS: Offline mode (no external data)"),
                    Line::from(""),
                    Line::from("Press 'n' for Network tab, 'q' to quit"),
                ],
                1 => vec![
                    Line::from(Span::styled("Network Seismology", Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD))),
                    Line::from(""),
                    Line::from("Block Coherence: 99.9% (Stable)"),
                    Line::from("Geographic Jitter: 25.3ms"),
                    Line::from("Gas Strategy: Standard"),
                    Line::from(""),
                    Line::from("Live Block Propagation:"),
                    Line::from("Block 1001: [15ms] PublicNode | [25ms] Ankr"),
                    Line::from("Block 1000: [18ms] PublicNode | [22ms] Ankr"),
                    Line::from(""),
                    Line::from("(Simulated data - NATS offline)"),
                ],
                2 => vec![
                    Line::from(Span::styled("Strategies", Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD))),
                    Line::from(""),
                    Line::from("- Zen Geometer: Educational Mode"),
                    Line::from("- Network Observer: Monitoring"),
                    Line::from("- All transactions simulated"),
                    Line::from(""),
                    Line::from("No real trading in simulation mode"),
                ],
                _ => vec![
                    Line::from(Span::styled("Help & Controls", Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD))),
                    Line::from(""),
                    Line::from("Navigation:"),
                    Line::from("  d/D - Dashboard"),
                    Line::from("  n/N - Network Seismology"),
                    Line::from("  s/S - Strategies"),
                    Line::from("  Tab - Cycle tabs"),
                    Line::from("  q/Q - Quit"),
                    Line::from(""),
                    Line::from("This is simulation mode - safe for learning!"),
                ],
            };

            let content_paragraph = Paragraph::new(content_lines)
                .block(Block::default().borders(Borders::ALL).title(tabs[current_tab]));
            f.render_widget(content_paragraph, chunks[1]);
        })?;

        // Handle input
        if event::poll(std::time::Duration::from_millis(100))? {
            if let Event::Key(key) = event::read()? {
                match key.code {
                    KeyCode::Char('q') | KeyCode::Char('Q') => running = false,
                    KeyCode::Char('n') | KeyCode::Char('N') => current_tab = 1,
                    KeyCode::Char('d') | KeyCode::Char('D') => current_tab = 0,
                    KeyCode::Char('s') | KeyCode::Char('S') => current_tab = 2,
                    KeyCode::Char('h') | KeyCode::Char('H') => current_tab = 3,
                    KeyCode::Tab => current_tab = (current_tab + 1) % tabs.len(),
                    _ => {}
                }
            }
        }
    }

    // Cleanup
    disable_raw_mode()?;
    execute!(terminal.backend_mut(), LeaveAlternateScreen, DisableMouseCapture)?;
    terminal.show_cursor()?;

    info!("Offline TUI simulation complete");
    Ok(())
}