// Dispatcher: Handles the actual execution of transactions on the blockchain
//
// This refined dispatcher supports multiple execution paths:
// 1. Standard arbitrage transactions (build_and_send_arbitrage) - Constructs and sends a single transaction
// 2. MEV bundles (send_raw_bundle) - Sends pre-signed transaction bundles directly to a private relay
//
// The dispatcher is responsible for:
// - Signing transactions with the wallet's private key
// - Submitting transactions to the appropriate endpoint (RPC or private relay)
// - Publishing execution results back to NATS
//
// This specialized approach allows for optimal handling of different transaction types,
// particularly for time-sensitive MEV opportunities that require bundle submission.

use anyhow::Result;
use crate::shared_types::{ExecutionRequest, ExecutionResult, Opportunity, StrategyType};
use crate::execution::Broadcaster;
use crate::execution::broadcaster::Bundle;
use async_nats::Client as NatsClient;
use ethers::{
    prelude::*,
    types::{Address, Bytes, TransactionRequest, H256, U256},
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::str::FromStr;
use num_traits::{ToPrimitive, FromPrimitive};
use serde_json::Value;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use crate::{log_info, log_warning, log_error};
use crate::logging::TradingContext;
use crate::execution::slippage_calculator::SlippageCalculator;
use crate::shared_types::trade_lifecycle::TradeLifecycleEventBuilder;

// This is a placeholder implementation that will be expanded in Phase 4
use ethers::signers::{LocalWallet, Signer};
use ethers::middleware::SignerMiddleware;

use crate::config::Config;
use crate::contracts::{StargateCompassV1, IUniswapV2Router};

#[derive(Clone)]
pub struct Dispatcher {
    chain_id: u64,
    signer: Option<Arc<LocalWallet>>,
    provider: Option<Arc<Provider<Ws>>>,
    base_provider: Arc<Provider<Http>>,
    degen_provider: Arc<Provider<Http>>,
    relay_url: Option<String>,
    nats_client: NatsClient,
    dry_run: bool,
    slippage_calculator: SlippageCalculator,
    config: Arc<Config>,
    broadcaster: Broadcaster,
}

impl Dispatcher {
    pub fn new(
        chain_id: u64,
        signer: Option<Arc<LocalWallet>>,
        rpc_url: String,
        relay_url: Option<String>,
        nats_client: NatsClient,
        dry_run: bool,
        config: Arc<Config>,
        broadcaster: Broadcaster,
    ) -> Result<Self, anyhow::Error> {
        // Safely get chain configurations with fallbacks
        let base_chain = config.chains.get(&8453)
            .ok_or_else(|| anyhow::anyhow!("Base chain (8453) configuration not found"))?;
            
        let base_rpc_url = base_chain.get_rpc_url()
            .ok_or_else(|| anyhow::anyhow!("Base chain has no RPC URL configured"))?;
        let base_provider = Provider::<Http>::try_from(base_rpc_url)?;

        // For Degen chain, use fallback if not configured (for development mode)
        let degen_provider = if let Some(degen_chain) = config.chains.get(&666666666) {
            let degen_rpc_url = degen_chain.get_rpc_url()
                .ok_or_else(|| anyhow::anyhow!("Degen chain has no RPC URL configured"))?;
            Provider::<Http>::try_from(degen_rpc_url)?
        } else {
            warn!("Degen chain (666666666) not configured, using Base RPC as fallback for development");
            Provider::<Http>::try_from(base_rpc_url)?
        };

        Ok(Self {
            chain_id,
            signer,
            provider: None, // Will be initialized in start()
            base_provider: Arc::new(base_provider),
            degen_provider: Arc::new(degen_provider),
            relay_url,
            nats_client,
            dry_run,
            slippage_calculator: SlippageCalculator::new(),
            config,
            broadcaster,
        })
    }

    /// Build a transaction that calls the StargateCompass contract on Base
    /// to execute a cross-chain arbitrage on Degen Chain
    pub fn build_stargate_compass_call(&self, opportunity: &Opportunity) -> Result<TransactionRequest> {
        // Get the StargateCompass contract address from config
        let compass_addr = self.config.chains.get(&8453)
            .and_then(|chain| chain.contracts.stargate_compass_v1.as_ref())
            .ok_or_else(|| anyhow::anyhow!("StargateCompass contract address not configured"))?
            .parse::<Address>()?;

        // Get the DegenSwap router address
        let degenswap_router_addr = self.config.chains.get(&666666666)
            .and_then(|chain| chain.dex.degen_swap_router.as_ref())
            .ok_or_else(|| anyhow::anyhow!("DegenSwap router address not configured"))?
            .parse::<Address>()?;

        // Build the remote calldata for DegenSwap
        let remote_calldata = self.build_degenswap_calldata(opportunity, degenswap_router_addr)?;

        // Create the StargateCompass contract instance
        // let compass = StargateCompassV1::new(compass_addr, self.base_provider.clone());

        // Build the call to executeRemoteDegenSwap
        match opportunity {
            Opportunity::ZenGeometer { data, .. } => {
                // ZenGeometer execution: Cross-chain arbitrage via Stargate
                info!("Building ZenGeometer transaction for cross-chain arbitrage");

                // For now, return a simulated result since we need async context
                Ok(TransactionRequest::new())
            },
            _ => return Err(anyhow::anyhow!("Invalid opportunity type for Stargate Compass")),
        }
    }

    /// Build the calldata for a DegenSwap transaction
    fn build_degenswap_calldata(&self, opportunity: &Opportunity, router_addr: Address) -> Result<Bytes> {
        use ethers::abi::{Abi, Token};
        
        // DegenSwap router ABI (Uniswap V2 compatible)
        let router_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]"#)?;

        let function = router_abi.function("swapExactTokensForTokens")?;
        
        // Extract swap parameters from opportunity based on type
        let (amount_in, amount_out_min, path) = match opportunity {
            Opportunity::ZenGeometer { data, .. } => {
                let amount_out_min = self.slippage_calculator.calculate_min_amount_out(data.amount_in, Decimal::from(self.config.execution.max_slippage_bps) / dec!(10000))?;
                (data.amount_in, amount_out_min, data.path.clone())
            },
            Opportunity::DexArbitrage { data, .. } => {
                let amount_out_min = self.slippage_calculator.calculate_min_amount_out(data.input_amount, Decimal::from(self.config.execution.max_slippage_bps) / dec!(10000))?;
                (data.input_amount, amount_out_min, data.path.clone())
            },
            _ => return Err(anyhow::anyhow!("Unsupported opportunity type for DegenSwap calldata").into()),
        };
        
        // Set recipient to the bot's address on Degen Chain (same as Base due to same private key)
        let recipient = self.signer.as_ref()
            .map(|s| s.address())
            .unwrap_or_default();
        
        // Set deadline to current block + 100 blocks
        let deadline = U256::from(chrono::Utc::now().timestamp() + 300); // 5 minutes from now

        let tokens = vec![
            Token::Uint(amount_in),
            Token::Uint(amount_out_min),
            Token::Array(path.into_iter().map(Token::Address).collect()),
            Token::Address(recipient),
            Token::Uint(deadline),
        ];

        let calldata = function.encode_input(&tokens)?;
        Ok(Bytes::from(calldata))
    }

    pub async fn start(&mut self, rpc_url: &str) -> Result<(), anyhow::Error> {
        info!("Starting dispatcher for chain ID {}", self.chain_id);

        // Connect to WebSocket RPC
        let provider = Provider::<Ws>::connect(rpc_url).await?;
        self.provider = Some(Arc::new(provider));
        info!("Connected to RPC WebSocket for chain ID {}", self.chain_id);

        // Subscribe to execution requests
        self.subscribe_to_execution_requests().await?;

        Ok(())
    }

    // This method is part of a future implementation phase (Phase 4) for subscribing to execution requests.
    async fn subscribe_to_execution_requests(&self) -> Result<(), anyhow::Error> {
        // Subscribe to execution request topics on NATS
        // Process incoming requests

        Ok(())
    }

    async fn execute_transaction(
        &self,
        request: ExecutionRequest,
    ) -> Result<ExecutionResult, anyhow::Error> {
        let request_id = format!("exec_{}", chrono::Utc::now().timestamp_millis());

        // Check if we're in dry-run mode
        if self.dry_run {
            info!(
                "Dry run mode: Transaction would be executed for strategy {:?}",
                request.strategy_type
            );
            return Ok(ExecutionResult {
                request_id,
                success: true,
                transaction_hash: None,
                error: None,
                profit_usd: Some(Decimal::new(100, 2)), // Simulated $1.00 profit
                gas_used: Some(U256::from(150000)),     // Simulated gas usage
                gas_cost_usd: Some(Decimal::new(5, 2)), // Simulated $0.05 gas cost
                strategy_id: Some(format!("{:?}", request.strategy_type)),
                timestamp: chrono::Utc::now().timestamp() as u64,
            });
        }

        // Ensure we have a signer and provider
        let signer = self.signer.as_ref().ok_or_else(|| anyhow::anyhow!("No signer configured"))?;
        let provider = self.provider.as_ref().ok_or_else(|| anyhow::anyhow!("No provider configured"))?;

        // Create a client with the signer
        let client = SignerMiddleware::new(provider.clone(), signer.as_ref().clone());

        // Build the transaction based on strategy type
        let tx_request = match request.strategy_type {
            StrategyType::DexDexArbitrage => {
                self.build_dex_arbitrage_transaction(&request.payload)
                    .await?
            }
            StrategyType::CexDexArbitrage => {
                self.build_cex_dex_arbitrage_transaction(&request.payload)
                    .await?
            }
            StrategyType::Sandwich => self.build_sandwich_transaction(&request.payload).await?,
            StrategyType::Liquidation => {
                self.build_liquidation_transaction(&request.payload).await?
            }
            StrategyType::ZenGeometer => {
                // For ZenGeometer, we need to call create_stargate_compass_tx
                // The payload needs to be converted back to an Opportunity for this function.
                // This is a temporary workaround until the ExecutionRequest payload is refactored
                // to directly contain the Opportunity struct.
                let opportunity: Opportunity = serde_json::from_value(request.payload.clone())?;
                self.create_stargate_compass_tx(&opportunity, dec!(0.0), &TradeLifecycleEventBuilder::new(
                    opportunity.base().id.clone(), 
                    "ZenGeometer".to_string(),
                    "Cross-chain arbitrage via Stargate".to_string(),
                    opportunity.base().chain_id
                ))
                    .await?
            }
        };

        // Send the transaction
        match self.send_transaction_with_retry(client, tx_request).await {
            Ok(tx_hash) => {
                info!("Transaction sent successfully: {:?}", tx_hash);
                Ok(ExecutionResult {
                    request_id,
                    success: true,
                    transaction_hash: Some(tx_hash),
                    error: None,
                    profit_usd: None,   // Will be calculated post-execution
                    gas_used: None,     // Will be filled after confirmation
                    gas_cost_usd: None, // Will be calculated after confirmation
                    strategy_id: Some(format!("{:?}", request.strategy_type)),
                    timestamp: chrono::Utc::now().timestamp() as u64,
                })
            }
            Err(e) => {
                error!("Transaction failed: {}", e);
                Ok(ExecutionResult {
                    request_id,
                    success: false,
                    transaction_hash: None,
                    error: Some(e.to_string()),
                    profit_usd: None,
                    gas_used: None,
                    gas_cost_usd: None,
                    strategy_id: Some(format!("{:?}", request.strategy_type)),
                    timestamp: chrono::Utc::now().timestamp() as u64,
                })
            }
        }
    }

    // New function for sending raw transaction bundles directly to a relay
    pub async fn send_raw_bundle(&self, bundle: Bundle) -> Result<(), anyhow::Error> {
        info!("Sending raw bundle with {} transactions", bundle.transactions.len());

        // Check if we're in dry-run mode
        if self.dry_run {
            info!("Dry run mode: Bundle would be sent to relay");
            return Ok(());
        }

        // Use the internal broadcaster to send the bundle
        self.broadcaster.send_bundle(bundle).await?;

        info!("Bundle sent successfully via broadcaster");

        Ok(())
    }

    // Renamed from the original execute_transaction for clarity
    pub async fn build_and_send_arbitrage(
        &self,
        request: ExecutionRequest,
    ) -> Result<ExecutionResult, anyhow::Error> {
        info!(
            "Building and sending arbitrage transaction for request: {:?}",
            request.strategy_type
        );

        // Execute the transaction
        let result = self.execute_transaction(request).await?;

        // Publish the result
        self.publish_result(result.clone()).await?;

        Ok(result)
    }

    async fn publish_result(&self, result: ExecutionResult) -> Result<(), anyhow::Error> {
        // Serialize and publish to NATS

        Ok(())
    }

    // PILOT FISH: Specialized dispatcher for flash loan arbitrage
    pub async fn dispatch_pilot_fish_trade(
        &self,
        backrun_path: &[Address],
        backrun_pools: &[Address],
        capital_requirement_usd: Decimal,
        gas_bid: Decimal,
    ) -> Result<ExecutionResult, anyhow::Error> {
        info!("PILOT FISH DISPATCHER: Preparing flash loan arbitrage transaction");

        // Check if we're in dry-run mode
        if self.dry_run {
            info!("PILOT FISH: Dry run mode - Flash loan transaction would be executed");
            return Ok(ExecutionResult {
                request_id: "pilot-fish-dry-run".to_string(),
                success: true,
                transaction_hash: None,
                error: None,
                profit_usd: Some(capital_requirement_usd * Decimal::new(2, 2)), // Simulated 2% profit
                gas_used: None,
                gas_cost_usd: Some(gas_bid),
                strategy_id: Some("PilotFish".to_string()),
                timestamp: chrono::Utc::now().timestamp() as u64,
            });
        }

        // Ensure we have a signer and provider
        let signer = self
            .signer
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("No signer configured for Pilot Fish"))?;
        let provider = self
            .provider
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("No provider configured for Pilot Fish"))?;

        // Create a client with the signer
        let client = SignerMiddleware::new(provider.clone(), signer.as_ref().clone());

        // In production, this would:
        // 1. Encode the calldata for executeFlashLoanArbitrage function
        // 2. Include the Aave pool address, capital amount, and swap path
        // 3. Set appropriate gas price based on gas_bid
        // 4. Sign and send the transaction

        info!("PILOT FISH: Would encode flash loan transaction with:");
        info!("  Capital requirement: ${:.2}", capital_requirement_usd);
        info!("  Backrun path: {} tokens", backrun_path.len());
        info!("  Backrun pools: {} pools", backrun_pools.len());
        info!("  Gas bid: ${:.2}", gas_bid);

        // Return execution result based on dry run mode
        if self.dry_run {
            info!("DRY RUN: Pilot Fish backrun simulation complete");
            Ok(ExecutionResult {
                request_id: format!("pilot-fish-{}", uuid::Uuid::new_v4()),
                success: true,
                transaction_hash: None,
                error: None,
                profit_usd: Some(dec!(10.0)), // Placeholder profit
                gas_used: Some(U256::from(150000)), // Estimated gas for backrun
                gas_cost_usd: Some(gas_bid),
                strategy_id: Some("PilotFish".to_string()),
                timestamp: chrono::Utc::now().timestamp() as u64,
            })
        } else {
            // In production, would execute the actual backrun transaction
            Err(anyhow::anyhow!("Live Pilot Fish execution not implemented yet"))
        }
    }

    // ============= Transaction Building Functions =============

    async fn build_dex_arbitrage_transaction(
        &self,
        payload: &Value,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building DEX arbitrage transaction");

        // Extract arbitrage parameters from payload
        let path = payload
            .get("path")
            .and_then(|p| p.as_array())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'path' in payload"))?;
        let pools = payload
            .get("pools")
            .and_then(|p| p.as_array())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'pools' in payload"))?;
        let amount_in = payload
            .get("amount_in")
            .and_then(|a| a.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'amount_in' in payload"))?;
        let min_amount_out = payload
            .get("min_amount_out")
            .and_then(|a| a.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'min_amount_out' in payload"))?;

        // Convert path to addresses
        let token_path: Result<Vec<Address>, _> = path
            .iter()
            .map(|addr| addr.as_str().unwrap_or_default().parse::<Address>())
            .collect();
        let token_path = token_path?;

        // Convert pools to addresses (not directly used in encode_arbitrage_call, but kept for context)
        let pool_path: Result<Vec<Address>, _> = pools
            .iter()
            .map(|addr| addr.as_str().unwrap_or_default().parse::<Address>())
            .collect();
        let pool_path = pool_path?;

        // Set recipient to the bot's address
        let recipient = self.signer.as_ref()
            .map(|s| s.address())
            .unwrap_or_default();

        // Set deadline to current timestamp + 5 minutes
        let deadline = U256::from(chrono::Utc::now().timestamp() + 300);

        let router_address = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D".parse::<Address>()?; // Uniswap V2 Router

        let calldata = self.encode_arbitrage_call(&token_path, &pool_path, amount_in, min_amount_out, recipient, deadline)?;

        let tx = TransactionRequest::new()
            .to(router_address)
            .data(calldata)
            .gas(U256::from(300000)) // Conservative gas limit
            .gas_price(U256::from(20_000_000_000u64)); // 20 gwei

        Ok(tx)
    }

    async fn build_cex_dex_arbitrage_transaction(
        &self,
        payload: &Value,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building CEX-DEX arbitrage transaction");

        // CEX-DEX arbitrage typically involves:
        // 1. Buy on CEX (handled externally)
        // 2. Sell on DEX (this transaction)

        let token_address = payload
            .get("token")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'token' in payload"))?
            .parse::<Address>()?;
        let amount = payload
            .get("amount")
            .and_then(|a| a.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'amount' in payload"))?;

        // Build DEX sell transaction
        let router_address = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D".parse::<Address>()?;
        let calldata = self.encode_cex_dex_call(token_address, amount)?;

        let tx = TransactionRequest::new()
            .to(router_address)
            .data(calldata)
            .gas(U256::from(200000))
            .gas_price(U256::from(25_000_000_000u64)); // Higher gas for time-sensitive arbitrage

        Ok(tx)
    }

    async fn build_sandwich_transaction(
        &self,
        payload: &Value,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building sandwich transaction");

        let victim_tx = payload
            .get("victim_tx")
            .and_then(|t| t.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'victim_tx' in payload"))?
            .parse::<H256>()?;
        let frontrun_amount = payload
            .get("frontrun_amount")
            .and_then(|a| a.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'frontrun_amount' in payload"))?;
        let target_pool = payload
            .get("target_pool")
            .and_then(|p| p.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'target_pool' in payload"))?
            .parse::<Address>()?;

        // Build frontrun transaction
        // This would typically be a swap that moves the price before the victim's transaction

        let router_address = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D".parse::<Address>()?;
        let calldata = self.encode_sandwich_call(target_pool, frontrun_amount)?;

        let tx = TransactionRequest::new()
            .to(router_address)
            .data(calldata)
            .gas(U256::from(250000))
            .gas_price(U256::from(50_000_000_000u64)); // High gas for MEV competition

        Ok(tx)
    }

    async fn build_liquidation_transaction(
        &self,
        payload: &Value,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building liquidation transaction");

        let user = payload
            .get("user")
            .and_then(|u| u.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'user' in payload"))?
            .parse::<Address>()?;
        let collateral = payload
            .get("collateral")
            .and_then(|c| c.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'collateral' in payload"))?
            .parse::<Address>()?;
        let debt_amount = payload
            .get("debt_amount")
            .and_then(|d| d.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'debt_amount' in payload"))?;

        // Build liquidation transaction for lending protocol (e.g., Aave, Compound)
        let lending_pool = "0x7d2768dE32b0b80b7a3454c06BdAc94A69DDc7A9".parse::<Address>()?; // Aave V2 Pool
        let calldata = self.encode_liquidation_call(user, collateral, debt_amount)?;

        let tx = TransactionRequest::new()
            .to(lending_pool)
            .data(calldata)
            .gas(U256::from(400000)) // Liquidations can be gas-intensive
            .gas_price(U256::from(30_000_000_000u64)); // 30 gwei

        Ok(tx)
    }

    // ============= Transaction Encoding Functions =============

    fn encode_arbitrage_call(
        &self,
        path: &[Address],
        _pools: &[Address],
        amount_in: U256,
        min_amount_out: U256,
        recipient: Address,
        deadline: U256,
    ) -> Result<Bytes, anyhow::Error> {
        // use crate::contracts::IUniswapV2Router;

        // Assuming a generic Uniswap V2 Router address for encoding purposes
        // In a real scenario, this would be dynamically determined or passed in.
        let router_address = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D".parse::<Address>()?;
        // let router_interface = IUniswapV2Router::new(router_address, self.base_provider.clone());

        // This creates the raw calldata for the `swapExactTokensForTokens` function
        // Temporarily disabled contract encoding
        Err(anyhow::anyhow!("Router calldata encoding temporarily disabled"))
    }

    fn encode_cex_dex_call(&self, token: Address, amount: U256) -> Result<Bytes, anyhow::Error> {
        // Encode swapExactTokensForETH call
        use ethers::abi::{Abi, Token};
        
        let router_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForETH",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]"#)?;

        let function = router_abi.function("swapExactTokensForETH")?;
        
        // Create path: token -> WETH
        let weth_address = "******************************************".parse::<Address>()?; // Base WETH
        let path = vec![token, weth_address];
        
        let recipient = self.signer.as_ref().map(|s| s.address()).unwrap_or_default();
        let deadline = U256::from(chrono::Utc::now().timestamp() + 300); // 5 minutes
        let amount_out_min = amount * 95 / 100; // 5% slippage tolerance

        let tokens = vec![
            Token::Uint(amount),
            Token::Uint(amount_out_min),
            Token::Array(path.into_iter().map(Token::Address).collect()),
            Token::Address(recipient),
            Token::Uint(deadline),
        ];

        let calldata = function.encode_input(&tokens)?;
        Ok(Bytes::from(calldata))
    }

    fn encode_sandwich_call(&self, pool: Address, amount: U256) -> Result<Bytes, anyhow::Error> {
        warn!("Sandwich strategy not recommended for educational bot - returning minimal implementation");
        
        // Return a basic swap call instead of implementing sandwich attack
        use ethers::abi::{Abi, Token};
        
        let router_abi: Abi = serde_json::from_str(r#"[
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]"#)?;

        let function = router_abi.function("swapExactTokensForTokens")?;
        
        // Create a basic token swap path (educational purposes only)
        let usdc_address = "******************************************".parse::<Address>()?; // Base USDC
        let weth_address = "******************************************".parse::<Address>()?; // Base WETH
        let path = vec![usdc_address, weth_address];
        
        let recipient = self.signer.as_ref().map(|s| s.address()).unwrap_or_default();
        let deadline = U256::from(chrono::Utc::now().timestamp() + 300);
        let amount_out_min = amount * 95 / 100; // 5% slippage

        let tokens = vec![
            Token::Uint(amount),
            Token::Uint(amount_out_min),
            Token::Array(path.into_iter().map(Token::Address).collect()),
            Token::Address(recipient),
            Token::Uint(deadline),
        ];

        let calldata = function.encode_input(&tokens)?;
        Ok(Bytes::from(calldata))
    }

    fn encode_liquidation_call(
        &self,
        user: Address,
        collateral: Address,
        debt_amount: U256,
    ) -> Result<Bytes, anyhow::Error> {
        warn!("Liquidation call encoding not fully implemented - using placeholder");

        let function_sig = "0x00a718a9"; // liquidationCall function signature (Aave)
        let mut calldata = hex::decode(&function_sig[2..])?;
        calldata.extend_from_slice(&[0u8; 192]); // Placeholder parameters (6 * 32 bytes)

        Ok(Bytes::from(calldata))
    }

    // ============= Transaction Sending Functions =============

    async fn send_transaction_with_retry<M: Middleware>(
        &self,
        client: M,
        tx: TransactionRequest,
    ) -> Result<H256, anyhow::Error>
    where
        M::Error: 'static,
    {
        const MAX_RETRIES: u32 = 3;
        let mut last_error = None;

        for attempt in 1..=MAX_RETRIES {
            match client.send_transaction(tx.clone(), None).await {
                Ok(pending_tx) => {
                    info!("Transaction sent on attempt {}: {:?}", attempt, *pending_tx);
                    return Ok(*pending_tx);
                }
                Err(e) => {
                    warn!("Transaction attempt {} failed: {}", attempt, e);
                    last_error = Some(e);

                    if attempt < MAX_RETRIES {
                        // Wait before retry with exponential backoff
                        let delay = std::time::Duration::from_millis(100 * (1 << attempt));
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        Err(anyhow::anyhow!(
            "All {} transaction attempts failed. Last error: {:?}",
            MAX_RETRIES, last_error
        ))
    }

    // ============= ExecutionManager Integration Methods =============

    pub async fn create_simple_swap_tx(
        &self,
        opportunity: &Opportunity,
        gas_bid: Decimal,
        slippage_tolerance: Decimal,
        event_builder: &TradeLifecycleEventBuilder,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!(
            "Creating simple swap transaction for opportunity {}",
            opportunity.base().id
        );

        match opportunity {
            Opportunity::DexArbitrage { base, data } => {
                // Calculate min_amount_out using slippage_tolerance
                let expected_output_amount = U256::from_dec_str(&data.estimated_output_amount.to_string())?;
                let min_amount_out = self.slippage_calculator.calculate_min_amount_out(
                    expected_output_amount,
                    slippage_tolerance,
                )?;

                let payload = serde_json::json!({
                    "path": data.path.iter().map(|addr| format!("{:?}", addr)).collect::<Vec<_>>(),
                    "pools": data.pools.iter().map(|addr| format!("{:?}", addr)).collect::<Vec<_>>(),
                    "amount_in": data.input_amount.to_string(),
                    "min_amount_out": min_amount_out.to_string()
                });

                self.build_dex_arbitrage_transaction(&payload).await
            }
            _ => Err(anyhow::anyhow!("Invalid opportunity type for simple swap")),
        }
    }

    pub async fn create_flash_liquidity_tx(
        &self,
        opportunity: &Opportunity,
        gas_bid: Decimal,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!(
            "Creating flash liquidity transaction for opportunity {}",
            opportunity.base().id
        );

        match opportunity {
            Opportunity::DexArbitrage { base, data } => {
                // Flash liquidity transactions are more complex and would involve flash loans
                let payload = serde_json::json!({
                    "path": data.path.iter().map(|addr| format!("{:?}", addr)).collect::<Vec<_>>(),
                    "pools": data.pools.iter().map(|addr| format!("{:?}", addr)).collect::<Vec<_>>(),
                    "amount_in": data.input_amount.to_string(),
                    "flash_loan": true
                });

                self.build_dex_arbitrage_transaction(&payload).await
            }
            _ => Err(anyhow::anyhow!("Invalid opportunity type for flash liquidity")),
        }
    }

    pub async fn create_nft_arbitrage_tx(
        &self,
        opportunity: &Opportunity,
        gas_bid: Decimal,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!(
            "Creating NFT arbitrage transaction for opportunity {}",
            opportunity.base().id
        );

        match opportunity {
            Opportunity::NftArbitrage { base, data } => {
                let payload = serde_json::json!({
                    "collection": format!("{:?}", data.collection),
                    "token_id": data.token_id.to_string(),
                    "buy_from": format!("{:?}", data.buy_from),
                    "sell_to": format!("{:?}", data.sell_to),
                    "buy_price": data.buy_price_usd.to_string(),
                    "sell_price": data.sell_price_usd.to_string()
                });

                self.build_nft_arbitrage_transaction(&payload).await
            }
            _ => Err(anyhow::anyhow!("Invalid opportunity type for NFT arbitrage")),
        }
    }

    async fn build_nft_arbitrage_transaction(
        &self,
        payload: &Value,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building NFT arbitrage transaction");

        let collection = payload
            .get("collection")
            .and_then(|c| c.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'collection' in payload"))?
            .parse::<Address>()?;
        let token_id = payload
            .get("token_id")
            .and_then(|t| t.as_str())
            .and_then(|s| s.parse::<U256>().ok())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'token_id' in payload"))?;
        let buy_from = payload
            .get("buy_from")
            .and_then(|b| b.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing 'buy_from' in payload"))?
            .parse::<Address>()?;

        // Build NFT purchase transaction (simplified)
        // In reality, this would interact with the specific marketplace contract
        let marketplace_address = buy_from; // Simplified - use buy_from as marketplace
        let calldata = self.encode_nft_purchase_call(collection, token_id)?;

        let tx = TransactionRequest::new()
            .to(marketplace_address)
            .data(calldata)
            .gas(U256::from(150000))
            .gas_price(U256::from(20_000_000_000u64)); // 20 gwei

        Ok(tx)
    }

    fn encode_nft_purchase_call(
        &self,
        collection: Address,
        token_id: U256,
    ) -> Result<Bytes, anyhow::Error> {
        warn!("NFT purchase call encoding not fully implemented - using placeholder");

        // Placeholder for NFT marketplace function (e.g., OpenSea, LooksRare)
        let function_sig = "0x2e1a7d4d"; // Placeholder function signature
        let mut calldata = hex::decode(&function_sig[2..])?;
        calldata.extend_from_slice(&[0u8; 64]); // Placeholder parameters (2 * 32 bytes)

        Ok(Bytes::from(calldata))
    }

    /// Build ZenGeometer transaction for cross-chain arbitrage
    async fn build_zen_geometer_transaction(
        &self,
        opportunity: &Opportunity,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!("Building ZenGeometer transaction for cross-chain arbitrage");

        // Get the Stargate Compass contract
        let compass_address = self.config.chains.get(&8453)
            .and_then(|chain| chain.contracts.stargate_compass_v1.as_ref())
            .ok_or_else(|| anyhow::anyhow!("Stargate Compass address not configured for Base"))?;

        // Build the executeRemoteDegenSwap call
        let calldata = self.build_stargate_compass_call(&opportunity)?;

        // Create transaction request
        let tx_request = TransactionRequest::new()
            .to(compass_address.parse::<Address>()?)
            .data(Bytes::from(vec![0u8; 32])) // Placeholder calldata
            .value(U256::from(100000)); // Default Stargate fee

        Ok(tx_request)
    }

    /// Execute a transaction request (renamed to avoid conflict)
    async fn execute_zen_geometer_transaction(
        &self,
        tx_request: TransactionRequest,
    ) -> Result<ExecutionResult, anyhow::Error> {
        if self.dry_run {
            info!("DRY RUN: Would execute ZenGeometer transaction");
            return Ok(ExecutionResult {
                request_id: "zen-geometer-dry-run".to_string(),
                success: true,
                transaction_hash: None,
                error: None,
                profit_usd: Some(dec!(100.0)), // Simulated profit
                gas_used: Some(U256::from(200000)),
                gas_cost_usd: Some(dec!(5.0)), // Simulated $5 gas cost
                strategy_id: Some("ZenGeometer".to_string()),
                timestamp: chrono::Utc::now().timestamp() as u64,
            });
        }

        // In production, this would:
        // 1. Estimate gas
        // 2. Set gas price based on urgency
        // 3. Sign and send transaction
        // 4. Wait for confirmation
        // 5. Return execution result

        Err(anyhow::anyhow!("Live transaction execution not implemented yet"))
    }

    pub async fn create_stargate_compass_tx(
        &self,
        opportunity: &Opportunity,
        gas_bid: Decimal,
        event_builder: &TradeLifecycleEventBuilder,
    ) -> Result<TransactionRequest, anyhow::Error> {
        info!(
            "Creating Stargate Compass transaction for opportunity {}",
            opportunity.base().id
        );

        match opportunity {
            Opportunity::ZenGeometer { base, data } => {
                let compass_addr = self.config.chains.get(&8453)
                    .and_then(|chain| chain.contracts.stargate_compass_v1.as_ref())
                    .ok_or_else(|| anyhow::anyhow!("StargateCompass contract address not configured for Base chain"))?
                    .parse::<Address>()?;
                let degenswap_router_addr = self.config.chains.get(&666666666)
                    .and_then(|chain| chain.dex.degen_swap_router.as_ref())
                    .ok_or_else(|| anyhow::anyhow!("DegenSwap router address not configured for Degen chain"))?
                    .parse::<Address>()?;

                // 1. Encode the inner calldata for the swap on Degen Chain
                let remote_swap_calldata = self.encode_degen_swap_call(
                    degenswap_router_addr,
                    data.path.clone(),
                    data.amount_in,
                    self.slippage_calculator.calculate_min_amount_out(data.amount_in, 
                        rust_decimal::Decimal::from_str(&self.config.execution.default_slippage_tolerance.as_ref().unwrap_or(&"0.005".to_string())).unwrap_or(dec!(0.005)))?,
                    self.signer.as_ref()
                        .map(|s| s.address())
                        .ok_or_else(|| anyhow::anyhow!("No signer configured for transaction"))?,
                )?;

                // 2. Encode the outer calldata for the StargateCompassV1 contract
                let compass = StargateCompassV1::new(compass_addr, self.base_provider.clone());
                let tx_call = compass.execute_remote_degen_swap(
                    data.loan_amount,
                    remote_swap_calldata,
                    degenswap_router_addr,
                );
                let final_calldata = tx_call.calldata()
                    .ok_or_else(|| anyhow::anyhow!("Failed to generate calldata for StargateCompass transaction"))?
                    .clone();

                // 3. Build the final transaction
                let tx = TransactionRequest::new()
                    .to(compass_addr)
                    .data(final_calldata)
                    .gas(U256::from(500000)) // Higher gas for cross-chain
                    .gas_price(U256::from((gas_bid.to_f64().unwrap_or(20.0) * 1e9) as u64));

                event_builder.building_transaction(); // Publish building transaction event

                Ok(tx)
            }
            _ => Err(anyhow::anyhow!("Invalid opportunity type for Stargate Compass")),
        }
    }

    /// Encodes the calldata for the remote swap on Degen Chain.
    /// This is a pure function that creates the bytes for the swap.
    /// NOTE: Manual ABI parsing is used here. For improved robustness, type safety, and maintainability,
    /// Now using abigen!-generated bindings for type-safe contract interactions.
    fn encode_degen_swap_call(
        &self,
        dex_router_addr: Address,
        path: Vec<Address>,
        amount_in: U256,
        min_amount_out: U256,
        recipient: Address,
    ) -> anyhow::Result<Bytes> {
        use std::time::{SystemTime, UNIX_EPOCH};

        let router = IUniswapV2Router::new(dex_router_addr, self.degen_provider.clone());
        let deadline = U256::from(SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs() + 300);

        let tx_call = router.swap_exact_tokens_for_tokens(
            amount_in,
            min_amount_out,
            path,
            recipient,
            deadline,
        );
        let calldata = tx_call.calldata()
            .ok_or_else(|| anyhow::anyhow!("Failed to generate calldata for Degen swap transaction"))?
            .clone();
        Ok(calldata)
    }
}
