// src/validation/configuration_validator_demo.rs

//! Configuration Validation Demo
//! 
//! This module provides demonstration capabilities for the configuration and
//! infrastructure validation system, showcasing parameter validation, network
//! connectivity testing, database validation, NATS messaging, contract verification,
//! and security parameter validation.

use crate::config::Config;
use crate::validation::configuration_validator::{ConfigurationValidator, SecurityValidationConfig};
use crate::validation::ValidationFrameworkResult;
use std::time::Duration;
use tracing::{info, warn};

/// Run comprehensive configuration validation demo
pub async fn run_configuration_validation_demo() -> ValidationFrameworkResult<()> {
    println!("🎯 Configuration and Infrastructure Validation Demo");
    println!("==================================================");
    println!("This demo showcases comprehensive validation of configuration parameters,");
    println!("network connectivity, database connections, NATS messaging, smart contracts,");
    println!("and security parameters for the Zen Geometer trading system.");
    println!();

    // Load configuration
    println!("📋 Step 1: Loading Configuration");
    println!("--------------------------------");
    let config = match Config::load() {
        Ok(config) => {
            println!("✅ Configuration loaded successfully");
            println!("   App Name: {}", config.app_name);
            println!("   Log Level: {}", config.log_level);
            println!("   Chains Configured: {}", config.chains.len());
            println!("   Strategies Enabled: {}", config.strategy.enabled_strategies.len());
            config
        }
        Err(e) => {
            println!("❌ Failed to load configuration: {}", e);
            println!("💡 Using default configuration for demo purposes");
            Config::default()
        }
    };

    // Create validator with demo settings
    let validator = ConfigurationValidator::with_settings(
        config,
        Duration::from_secs(5), // Short timeout for demo
        false, // Enable connectivity tests
        SecurityValidationConfig {
            validate_private_keys: false, // Skip for demo safety
            check_production_keys: false,
            min_key_entropy: 64,
        },
    );

    // Demo 1: Parameter Validation
    println!("\n🔍 Step 2: Parameter Validation");
    println!("-------------------------------");
    println!("Validating configuration parameters for correctness and safety...");
    
    match validator.validate_parameters().await {
        Ok(metrics) => {
            println!("✅ Parameter validation completed");
            println!("   Parameters Validated: {}", metrics.parameters_validated);
            println!("   Validation Errors: {}", metrics.validation_errors);
            println!("   Warnings: {}", metrics.warnings);
            
            // Show some example results
            let mut shown = 0;
            for (param_name, result) in &metrics.parameter_results {
                if shown < 3 {
                    println!("   {} {}: {}", 
                        result.status.to_color_code(),
                        param_name,
                        result.message
                    );
                    shown += 1;
                }
            }
            if metrics.parameter_results.len() > 3 {
                println!("   ... and {} more parameters validated", metrics.parameter_results.len() - 3);
            }
        }
        Err(e) => {
            println!("❌ Parameter validation failed: {}", e);
        }
    }

    // Demo 2: Network Validation (Offline Mode for Demo)
    println!("\n🌐 Step 3: Network Endpoint Validation");
    println!("--------------------------------------");
    println!("Testing network connectivity and RPC endpoint failover...");
    
    match validator.validate_network_endpoints_offline().await {
        Ok(metrics) => {
            println!("✅ Network validation completed (offline mode)");
            println!("   Endpoints Tested: {}", metrics.endpoints_tested);
            println!("   Format Validation Passed: {}", metrics.successful_connections);
            println!("   Format Validation Failed: {}", metrics.failed_connections);
            
            // Show chain-specific results
            for (chain_id, result) in metrics.failover_test_results.iter().take(2) {
                println!("   Chain {}: {} Primary: {}", 
                    chain_id,
                    if result.failover_successful { "✅" } else { "❌" },
                    result.primary_status.url
                );
                if !result.backup_endpoints.is_empty() {
                    println!("     Backup Endpoints: {}", result.backup_endpoints.len());
                }
            }
        }
        Err(e) => {
            println!("❌ Network validation failed: {}", e);
        }
    }

    // Demo 3: Database Validation (Configuration Only)
    println!("\n🗄️  Step 4: Database Configuration Validation");
    println!("---------------------------------------------");
    println!("Validating database connection settings and pool configuration...");
    
    match validator.validate_database_configuration().await {
        Ok(metrics) => {
            println!("✅ Database configuration validation completed");
            println!("   PostgreSQL Config: {}", 
                if metrics.postgres_status.connected { "✅ Valid" } else { "❌ Invalid" }
            );
            println!("   Redis Config: {}", 
                if metrics.redis_status.connected { "✅ Valid" } else { "❌ Invalid" }
            );
            println!("   Connection Pool: {}", 
                if metrics.connection_pool_status.configuration_valid { "✅ Valid" } else { "❌ Invalid" }
            );
            println!("   SSL Configuration: {}", 
                if metrics.ssl_validation.ssl_enabled { "✅ Enabled" } else { "⚠️  Disabled" }
            );
            
            // Show pool configuration messages
            if !metrics.connection_pool_status.messages.is_empty() {
                println!("   Pool Messages:");
                for message in metrics.connection_pool_status.messages.iter().take(2) {
                    println!("     - {}", message);
                }
            }
        }
        Err(e) => {
            println!("❌ Database validation failed: {}", e);
        }
    }

    // Demo 4: NATS Validation (Configuration Only)
    println!("\n📨 Step 5: NATS Messaging Validation");
    println!("------------------------------------");
    println!("Validating NATS messaging configuration and security settings...");
    
    match validator.validate_nats_configuration().await {
        Ok(metrics) => {
            println!("✅ NATS configuration validation completed");
            println!("   Connection Config: {}", 
                if metrics.connection_status.connected { "✅ Valid" } else { "❌ Invalid" }
            );
            println!("   TLS Enabled: {}", 
                if metrics.connection_status.tls_enabled { "✅ Yes" } else { "⚠️  No" }
            );
            println!("   Security Validation:");
            println!("     TLS Valid: {}", 
                if metrics.security_validation.tls_valid { "✅" } else { "⚠️" }
            );
            println!("     Auth Configured: {}", 
                if metrics.security_validation.auth_configured { "✅" } else { "⚠️" }
            );
            println!("     Permissions Valid: {}", 
                if metrics.security_validation.permissions_valid { "✅" } else { "❌" }
            );
        }
        Err(e) => {
            println!("❌ NATS validation failed: {}", e);
        }
    }

    // Demo 5: Contract Address Validation
    println!("\n📜 Step 6: Smart Contract Validation");
    println!("------------------------------------");
    println!("Validating smart contract addresses and ABI consistency...");
    
    match validator.validate_contract_addresses().await {
        Ok(metrics) => {
            println!("✅ Contract validation completed");
            println!("   Overall Status: {} {:?}", 
                metrics.overall_status.to_color_code(),
                metrics.overall_status
            );
            println!("   Addresses Validated: {}", metrics.address_validation.len());
            println!("   ABI Consistency Checks: {}", metrics.abi_consistency.len());
            println!("   Deployment Verifications: {}", metrics.deployment_verification.len());
            
            // Show contract results
            let mut shown = 0;
            for (contract_name, validation) in &metrics.address_validation {
                if shown < 2 {
                    println!("   {}: {} Format: {} Exists: {}", 
                        contract_name,
                        if validation.address_format_valid { "✅" } else { "❌" },
                        if validation.address_format_valid { "✅" } else { "❌" },
                        if validation.contract_exists { "✅" } else { "❌" }
                    );
                    shown += 1;
                }
            }
            if metrics.address_validation.len() > 2 {
                println!("   ... and {} more contracts validated", metrics.address_validation.len() - 2);
            }
        }
        Err(e) => {
            println!("❌ Contract validation failed: {}", e);
        }
    }

    // Demo 6: Security Validation (Limited for Demo)
    println!("\n🔒 Step 7: Security Parameter Validation");
    println!("----------------------------------------");
    println!("Validating security configuration and environment protection...");
    
    match validator.validate_security_parameters().await {
        Ok(metrics) => {
            println!("✅ Security validation completed");
            println!("   Overall Status: {} {:?}", 
                metrics.overall_security_status.to_color_code(),
                metrics.overall_security_status
            );
            println!("   Environment: {}", metrics.environment_security.environment_type);
            println!("   Security Requirements Met: {}", 
                if metrics.environment_security.security_requirements_met { "✅" } else { "❌" }
            );
            println!("   Sensitive Data Protected: {}", 
                if metrics.environment_security.sensitive_data_protected { "✅" } else { "❌" }
            );
            println!("   Access Controls Valid: {}", 
                if metrics.environment_security.access_controls_valid { "✅" } else { "❌" }
            );
            
            // Show API key validation (safe to display counts)
            if !metrics.api_key_validation.is_empty() {
                println!("   API Keys Validated: {}", metrics.api_key_validation.len());
                for (service_name, validation) in metrics.api_key_validation.iter().take(2) {
                    println!("     {}: {} Length: {}", 
                        service_name,
                        if validation.format_valid { "✅" } else { "❌" },
                        if validation.length_adequate { "✅" } else { "⚠️" }
                    );
                }
            }
        }
        Err(e) => {
            println!("❌ Security validation failed: {}", e);
        }
    }

    // Demo Summary
    println!("\n🎉 Configuration Validation Demo Complete!");
    println!("==========================================");
    println!("This demo showcased validation of:");
    println!("  ✅ Configuration parameters (Kelly fraction, slippage, gas limits)");
    println!("  ✅ Network endpoints (RPC URLs, failover capabilities)");
    println!("  ✅ Database settings (PostgreSQL, Redis, connection pools)");
    println!("  ✅ NATS messaging (security, TLS, authentication)");
    println!("  ✅ Smart contracts (addresses, ABI consistency, deployment)");
    println!("  ✅ Security parameters (environment protection, key validation)");
    println!();
    println!("💡 Real-World Usage:");
    println!("   • Run before deployment to catch configuration issues");
    println!("   • Integrate into CI/CD pipelines for automated validation");
    println!("   • Use for troubleshooting connectivity and configuration problems");
    println!("   • Validate security compliance across environments");
    println!();
    println!("📚 Available Commands:");
    println!("   cargo run -- validation configuration comprehensive");
    println!("   cargo run -- validation configuration parameters");
    println!("   cargo run -- validation configuration network --offline");
    println!("   cargo run -- validation configuration security --skip-private-keys");

    Ok(())
}

/// Run quick configuration validation for testing
pub async fn run_quick_configuration_validation() -> ValidationFrameworkResult<()> {
    info!("Running quick configuration validation");
    
    let config = Config::load().unwrap_or_default();
    let validator = ConfigurationValidator::new(config);
    
    // Quick parameter validation
    let param_result = validator.validate_parameters().await?;
    info!("Parameter validation: {} parameters, {} errors, {} warnings",
        param_result.parameters_validated,
        param_result.validation_errors,
        param_result.warnings
    );
    
    // Quick network validation (offline)
    let network_result = validator.validate_network_endpoints_offline().await?;
    info!("Network validation: {} endpoints tested, {} successful",
        network_result.endpoints_tested,
        network_result.successful_connections
    );
    
    // Quick contract validation
    let contract_result = validator.validate_contract_addresses().await?;
    info!("Contract validation: {} addresses validated, status: {:?}",
        contract_result.address_validation.len(),
        contract_result.overall_status
    );
    
    println!("✅ Quick configuration validation completed successfully");
    Ok(())
}

/// Show configuration validation usage examples
pub fn show_configuration_validation_usage_examples() {
    println!("🔧 Configuration and Infrastructure Validation");
    println!("==============================================");
    println!();
    
    println!("📋 Basic Validation Commands:");
    println!("  # Comprehensive validation (all components)");
    println!("  cargo run -- validation configuration comprehensive");
    println!();
    println!("  # Individual component validation");
    println!("  cargo run -- validation configuration parameters");
    println!("  cargo run -- validation configuration network");
    println!("  cargo run -- validation configuration database");
    println!("  cargo run -- validation configuration nats");
    println!("  cargo run -- validation configuration contracts");
    println!("  cargo run -- validation configuration security");
    println!();
    
    println!("🌐 Network Validation Options:");
    println!("  # Offline mode (format validation only)");
    println!("  cargo run -- validation configuration network --offline");
    println!();
    println!("  # Custom timeout");
    println!("  cargo run -- validation configuration network --timeout 30");
    println!();
    println!("  # Comprehensive offline validation");
    println!("  cargo run -- validation configuration comprehensive --offline");
    println!();
    
    println!("🔒 Security Validation Options:");
    println!("  # Skip private key validation (for safety)");
    println!("  cargo run -- validation configuration security --skip-private-keys");
    println!();
    println!("  # Skip production environment checks");
    println!("  cargo run -- validation configuration security --skip-production-checks");
    println!();
    println!("  # Skip all security validation");
    println!("  cargo run -- validation configuration comprehensive --skip-security");
    println!();
    
    println!("🗄️  Database and Messaging Options:");
    println!("  # Offline database validation (config only)");
    println!("  cargo run -- validation configuration database --offline");
    println!();
    println!("  # Offline NATS validation (config only)");
    println!("  cargo run -- validation configuration nats --offline");
    println!();
    
    println!("🎯 Demo and Examples:");
    println!("  # Interactive demo");
    println!("  cargo run -- validation configuration demo");
    println!();
    
    println!("⚙️  Environment Variables for Testing:");
    println!("  # Database configuration");
    println!("  export DATABASE_URL='postgresql://localhost:5432/basilisk'");
    println!("  export REDIS_URL='redis://localhost:6379'");
    println!("  export DATABASE_MAX_CONNECTIONS=20");
    println!("  export DATABASE_CONNECTION_TIMEOUT=30");
    println!("  export DATABASE_SSL_MODE=require");
    println!();
    println!("  # NATS configuration");
    println!("  export NATS_USER=username");
    println!("  export NATS_TOKEN=token");
    println!("  export NATS_CREDS=/path/to/creds.json");
    println!();
    
    println!("📊 What Gets Validated:");
    println!("  Parameter Validation:");
    println!("    ✅ Kelly fraction cap (0.0 < value ≤ 1.0, recommended ≤ 0.5)");
    println!("    ✅ Minimum profitability (1-10000 bps, recommended ≥ 10 bps)");
    println!("    ✅ Slippage tolerance (1-1000 bps, recommended ≤ 500 bps)");
    println!("    ✅ Gas price limits (1-1000 gwei, recommended ≤ 200 gwei)");
    println!("    ✅ Chain configurations (valid IDs, RPC URLs, gas settings)");
    println!();
    println!("  Network Validation:");
    println!("    ✅ RPC endpoint connectivity and response times");
    println!("    ✅ Failover capabilities and backup endpoint testing");
    println!("    ✅ URL format validation and accessibility");
    println!();
    println!("  Database Validation:");
    println!("    ✅ PostgreSQL/TimescaleDB connection and version");
    println!("    ✅ Redis connection and PING response");
    println!("    ✅ Connection pool configuration and capacity");
    println!("    ✅ SSL/TLS encryption settings");
    println!();
    println!("  NATS Validation:");
    println!("    ✅ Connection establishment and authentication");
    println!("    ✅ Subject subscription and publishing tests");
    println!("    ✅ TLS configuration and security settings");
    println!("    ✅ Permission validation");
    println!();
    println!("  Contract Validation:");
    println!("    ✅ Address format validation (0x + 40 hex characters)");
    println!("    ✅ Contract existence verification on blockchain");
    println!("    ✅ ABI consistency and function signature matching");
    println!("    ✅ Deployment verification and explorer confirmation");
    println!();
    println!("  Security Validation:");
    println!("    ✅ Private key format and entropy validation");
    println!("    ✅ Test key detection and production safety checks");
    println!("    ✅ API key format and length validation");
    println!("    ✅ Environment security and access control validation");
    println!();
    
    println!("🚀 Integration with Deployment:");
    println!("  The configuration validator integrates with the 5-tier deployment ladder:");
    println!("    • Simulate Mode: Validate all parameters before simulation");
    println!("    • Shadow Mode: Ensure network connectivity for Anvil forking");
    println!("    • Sentinel Mode: Validate contract addresses before small trades");
    println!("    • Low-Capital Mode: Confirm risk parameters and limits");
    println!("    • Live Mode: Full validation including security compliance");
}