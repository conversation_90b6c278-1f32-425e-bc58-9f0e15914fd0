// src/validation/lifecycle_validator_demo.rs

//! Demo module for lifecycle validator

use crate::validation::lifecycle_validator::{
    LifecycleValidator, LifecycleValidationConfig, LifecycleValidationMetrics
};
use crate::validation::{ValidationFrameworkResult, TestDataProvider};
use std::time::Duration;
use rust_decimal_macros::dec;
use tracing::{info, warn};

/// Run lifecycle validation demo
pub async fn run_lifecycle_validation_demo() -> ValidationFrameworkResult<()> {
    info!("🎬 Starting Lifecycle Validation Demo");
    info!("════════════════════════════════════════");

    // Demo configuration optimized for quick demonstration
    let config = LifecycleValidationConfig {
        max_lifecycle_duration: Duration::from_secs(3),
        max_detection_time: Duration::from_millis(30),
        max_scoring_time: Duration::from_millis(15),
        max_execution_time: Duration::from_millis(500),
        max_settlement_time: Duration::from_millis(800),
        min_profit_threshold_usd: dec!(0.5),
        concurrent_strategy_count: 2,
        adaptation_test_duration: Duration::from_secs(5),
        failure_injection_rate: 0.3,
        enable_cross_chain_testing: true,
        enable_concurrent_strategy_testing: true,
    };

    info!("⚙️ Demo Configuration:");
    info!("   • Max lifecycle duration: {}ms", config.max_lifecycle_duration.as_millis());
    info!("   • Detection timeout: {}ms", config.max_detection_time.as_millis());
    info!("   • Scoring timeout: {}ms", config.max_scoring_time.as_millis());
    info!("   • Execution timeout: {}ms", config.max_execution_time.as_millis());
    info!("   • Settlement timeout: {}ms", config.max_settlement_time.as_millis());
    info!("   • Min profit threshold: ${}", config.min_profit_threshold_usd);
    info!("   • Concurrent strategies: {}", config.concurrent_strategy_count);
    info!("   • Failure injection rate: {:.0}%", config.failure_injection_rate * 100.0);

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    info!("");
    info!("🧪 Generating demo test scenarios...");
    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let demo_scenarios = scenarios.into_iter().take(2).collect::<Vec<_>>();
    
    for (i, scenario) in demo_scenarios.iter().enumerate() {
        info!("   {}. {} - Regime: {:?}", i + 1, scenario.name, scenario.market_conditions.regime);
    }

    info!("");
    info!("🔄 Running complete trading workflow validation...");
    let workflow_metrics = validator.validate_complete_trading_workflow(&demo_scenarios).await?;

    display_demo_workflow_results(&workflow_metrics);

    info!("");
    info!("🔀 Running multi-strategy concurrent execution demo...");
    let multi_strategy_metrics = validator.validate_multi_strategy_concurrent_execution(&demo_scenarios).await?;

    display_demo_multi_strategy_results(&multi_strategy_metrics);

    info!("");
    info!("🌊 Running market adaptation demo...");
    let adaptation_metrics = validator.validate_market_condition_adaptation(&demo_scenarios[0]).await?;

    display_demo_adaptation_results(&adaptation_metrics);

    info!("");
    info!("🛡️ Running system resilience demo...");
    let resilience_metrics = validator.validate_system_resilience_and_recovery(&demo_scenarios).await?;

    display_demo_resilience_results(&resilience_metrics);

    info!("");
    info!("✅ Lifecycle Validation Demo Completed Successfully!");
    info!("════════════════════════════════════════");

    Ok(())
}

/// Run quick lifecycle validation for testing
pub async fn run_quick_lifecycle_validation() -> ValidationFrameworkResult<LifecycleValidationMetrics> {
    info!("⚡ Running quick lifecycle validation");

    let config = LifecycleValidationConfig {
        max_lifecycle_duration: Duration::from_secs(1),
        max_detection_time: Duration::from_millis(10),
        max_scoring_time: Duration::from_millis(5),
        max_execution_time: Duration::from_millis(100),
        max_settlement_time: Duration::from_millis(200),
        min_profit_threshold_usd: dec!(0.1),
        concurrent_strategy_count: 1,
        adaptation_test_duration: Duration::from_secs(2),
        failure_injection_rate: 0.1,
        enable_cross_chain_testing: false,
        enable_concurrent_strategy_testing: false,
    };

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let test_scenarios = scenarios.into_iter().take(1).collect::<Vec<_>>();

    let metrics = validator.validate_complete_trading_workflow(&test_scenarios).await?;

    info!("⚡ Quick validation completed: {}/{} successful", 
          metrics.successful_lifecycles, metrics.total_lifecycles_tested);

    Ok(metrics)
}

/// Display demo workflow results
fn display_demo_workflow_results(metrics: &LifecycleValidationMetrics) {
    info!("📊 Workflow Demo Results:");
    info!("   ┌─ Lifecycles tested: {}", metrics.total_lifecycles_tested);
    info!("   ├─ Successful: {} ({:.0}%)", 
          metrics.successful_lifecycles,
          (metrics.successful_lifecycles as f64 / metrics.total_lifecycles_tested as f64) * 100.0);
    info!("   ├─ Failed: {}", metrics.failed_lifecycles);
    info!("   └─ Average time: {}ms", metrics.average_lifecycle_time.as_millis());

    info!("   🔧 Pipeline Stages:");
    info!("      ├─ Detection: {:.0}% success, {}ms avg", 
          metrics.pipeline_performance.detection_performance.success_rate * 100.0,
          metrics.pipeline_performance.detection_performance.average_time.as_millis());
    info!("      ├─ Scoring: {:.0}% success, {}ms avg", 
          metrics.pipeline_performance.scoring_performance.success_rate * 100.0,
          metrics.pipeline_performance.scoring_performance.average_time.as_millis());
    info!("      ├─ Execution: {:.0}% success, {}ms avg", 
          metrics.pipeline_performance.execution_performance.success_rate * 100.0,
          metrics.pipeline_performance.execution_performance.average_time.as_millis());
    info!("      └─ Settlement: {:.0}% success, {}ms avg", 
          metrics.pipeline_performance.settlement_performance.success_rate * 100.0,
          metrics.pipeline_performance.settlement_performance.average_time.as_millis());

    info!("   💰 Profit Summary:");
    info!("      ├─ Total net profit: ${:.2}", metrics.profit_realization.total_net_profit_usd);
    info!("      ├─ Avg per opportunity: ${:.2}", metrics.profit_realization.average_profit_per_opportunity);
    info!("      └─ Realization rate: {:.0}%", metrics.profit_realization.profit_realization_rate * 100.0);
}

/// Display demo multi-strategy results
fn display_demo_multi_strategy_results(metrics: &crate::validation::lifecycle_validator::MultiStrategyPerformanceMetrics) {
    info!("📊 Multi-Strategy Demo Results:");
    info!("   ┌─ Concurrent strategies: {}", metrics.concurrent_strategies_tested);
    info!("   ├─ Coordination success: {:.0}%", metrics.coordination_success_rate * 100.0);
    info!("   └─ Resource conflicts: {}", metrics.resource_contention_incidents);

    if !metrics.individual_strategy_performance.is_empty() {
        info!("   🎯 Individual Strategy Performance:");
        for (strategy_name, performance) in &metrics.individual_strategy_performance {
            info!("      ├─ {}: {:.0}% success, ${:.2} avg profit", 
                  strategy_name, 
                  performance.success_rate * 100.0, 
                  performance.average_profit_usd);
        }
    }
}

/// Display demo adaptation results
fn display_demo_adaptation_results(metrics: &crate::validation::lifecycle_validator::MarketAdaptationPerformanceMetrics) {
    info!("📊 Market Adaptation Demo Results:");
    info!("   ┌─ Regime changes: {}", metrics.regime_changes_detected);
    info!("   ├─ Successful adaptations: {}", metrics.successful_adaptations);
    
    let success_rate = if metrics.regime_changes_detected > 0 {
        (metrics.successful_adaptations as f64 / metrics.regime_changes_detected as f64) * 100.0
    } else {
        0.0
    };
    
    info!("   ├─ Adaptation success rate: {:.0}%", success_rate);
    info!("   ├─ Avg adaptation time: {}ms", metrics.average_adaptation_time.as_millis());
    info!("   └─ Parameter accuracy: {:.0}%", metrics.parameter_adjustment_accuracy * 100.0);
}

/// Display demo resilience results
fn display_demo_resilience_results(metrics: &crate::validation::lifecycle_validator::ResiliencePerformanceMetrics) {
    info!("📊 System Resilience Demo Results:");
    info!("   ┌─ Failures injected: {}", metrics.failures_injected);
    info!("   ├─ Successful recoveries: {}", metrics.successful_recoveries);
    
    let recovery_rate = if metrics.failures_injected > 0 {
        (metrics.successful_recoveries as f64 / metrics.failures_injected as f64) * 100.0
    } else {
        100.0
    };
    
    info!("   ├─ Recovery success rate: {:.0}%", recovery_rate);
    info!("   ├─ Avg recovery time: {}ms", metrics.average_recovery_time.as_millis());
    info!("   └─ System availability: {:.1}%", metrics.system_availability * 100.0);
}

/// Show lifecycle validation usage examples
pub fn show_lifecycle_validation_usage_examples() {
    info!("");
    info!("📚 Lifecycle Validation Usage Examples:");
    info!("════════════════════════════════════════");
    info!("");
    info!("🔄 Complete Workflow Validation:");
    info!("   cargo run -- validation lifecycle complete-workflow \\");
    info!("     --scenario-count 10 \\");
    info!("     --max-duration 60 \\");
    info!("     --enable-cross-chain \\");
    info!("     --enable-concurrent");
    info!("");
    info!("🔀 Multi-Strategy Testing:");
    info!("   cargo run -- validation lifecycle multi-strategy \\");
    info!("     --strategy-count 5 \\");
    info!("     --duration 120");
    info!("");
    info!("🌊 Market Adaptation Testing:");
    info!("   cargo run -- validation lifecycle market-adaptation \\");
    info!("     --duration 300 \\");
    info!("     --regime-changes 3");
    info!("");
    info!("🛡️ System Resilience Testing:");
    info!("   cargo run -- validation lifecycle system-resilience \\");
    info!("     --failure-rate 0.15 \\");
    info!("     --duration 600");
    info!("");
    info!("🎯 Comprehensive Validation Suite:");
    info!("   # Full comprehensive test:");
    info!("   cargo run -- validation lifecycle comprehensive-suite --enable-all");
    info!("");
    info!("   # Quick comprehensive test:");
    info!("   cargo run -- validation lifecycle comprehensive-suite --quick");
    info!("");
    info!("🎬 Demo Mode:");
    info!("   cargo run -- validation lifecycle demo");
    info!("");
    info!("💡 Tips:");
    info!("   • Use --quick for faster testing during development");
    info!("   • Enable --enable-cross-chain for full cross-chain validation");
    info!("   • Adjust --failure-rate to test different resilience scenarios");
    info!("   • Use demo mode to understand the validation capabilities");
}

/// Run framework integration demo
pub async fn run_framework_integration_demo() -> ValidationFrameworkResult<()> {
    info!("🔗 Running Lifecycle Validator Framework Integration Demo");
    info!("════════════════════════════════════════════════════════");

    // Show how lifecycle validator integrates with the validation framework
    let config = LifecycleValidationConfig::default();
    let validator = LifecycleValidator::new(config)?;

    info!("✅ Lifecycle validator created successfully");
    info!("🔧 Configuration loaded with default settings");
    info!("🧪 Test data provider initialized");
    info!("🎭 Mock system components ready");
    info!("📊 Metrics collection system active");

    // Demonstrate quick validation
    info!("");
    info!("🚀 Running quick integration test...");
    let metrics = run_quick_lifecycle_validation().await?;

    info!("📈 Integration test results:");
    info!("   • Validation framework: ✅ Working");
    info!("   • Mock components: ✅ Functional");
    info!("   • Metrics collection: ✅ Active");
    info!("   • Pipeline tracking: ✅ Operational");

    if metrics.successful_lifecycles > 0 {
        info!("   • End-to-end validation: ✅ Successful");
    } else {
        warn!("   • End-to-end validation: ⚠️ Needs attention");
    }

    info!("");
    info!("🎉 Framework integration demo completed successfully!");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_quick_lifecycle_validation() {
        let result = run_quick_lifecycle_validation().await;
        assert!(result.is_ok());
        
        let metrics = result.unwrap();
        assert!(metrics.total_lifecycles_tested > 0);
    }

    #[tokio::test]
    async fn test_framework_integration_demo() {
        let result = run_framework_integration_demo().await;
        assert!(result.is_ok());
    }
}