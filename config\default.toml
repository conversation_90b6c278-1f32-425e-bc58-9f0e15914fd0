# Zen Geometer - Base Sepolia Testnet Configuration
# Updated with deployed contract addresses

# Global settings
dry_run = false
active_chain_id = 84532  # Base Sepolia
authorized_operators = ["testnet_operator"]

[database]
url = "postgres://basilisk:basilisk_password@localhost:5432/basilisk_db"

[redis]
url = "redis://localhost:6379"

[nats]
url = "nats://localhost:4222"
subjects = [
    "market.>",           # All market data (trades, prices, volumes)
    "network.blocks",     # New block notifications
    "gas.prices",         # Gas price updates
    "are.temporal_harmonics",    # Chronos Sieve FFT analysis
    "are.network_seismology",    # Network timing analysis
    "are.geometric_score",       # Mandorla Gauge opportunity scoring
    "are.fractal_analysis",      # Fractal market analysis
    "execution.trades",          # Trade execution results
    "risk.alerts"               # Risk management alerts
]
queue_group = "basilisk_ingestors"
max_reconnect_attempts = 10
reconnect_delay_ms = 1000

[rpc]
url = "https://sepolia.base.org"
ws_url = "wss://sepolia.base.org"

[execution]
gas_limit = 500000
max_priority_fee = 2000000000
default_slippage_tolerance = "0.005"
fallback_base_fee_gwei = "10.0"
min_net_profit_threshold = "2.0"
gas_multiplier_network_shock = "1.5"
gas_multiplier_high_sp_time = "1.2"
gas_multiplier_bot_gas_war = "2.0"
gas_multiplier_retail_fomo = "1.1"
gas_multiplier_high_volatility = "1.05"
network_shock_threshold_ms = 500

[execution.gas_buffer_multipliers]
low = "1.1"
medium = "1.2"
high = "1.3"
critical = "1.5"

[execution.priority_fee_gwei]
low_gwei = "1.0"
medium_gwei = "2.0"
high_gwei = "5.0"
critical_gwei = "10.0"

[risk]
max_position_size_usd = "1000.0"
max_slippage = "0.005"
max_gas_price = 100000000000
kelly_fraction = "0.25"
max_daily_loss_usd = "100.0"
max_strategy_loss_usd = "50.0"
max_consecutive_failures = 5

[risk.regime_multipliers]
high_volatility_position_mult = "0.5"
high_volatility_loss_mult = "0.25"
bot_gas_war_position_mult = "0.1"
bot_gas_war_loss_mult = "0.1"
retail_fomo_position_mult = "1.2"
retail_fomo_loss_mult = "1.5"
calm_orderly_position_mult = "1.0"
calm_orderly_loss_mult = "1.0"

[strategies.unified]
enabled = true
gaze_min_profit_usd = "5.0"
max_flash_exposure_usd = "1000.0"
bidding_aggressiveness_pct = "0.382"
min_net_profit_usd = "5.0"
risk_aversion_k = "0.5"

[strategies]
min_execution_score = "0.5"
quality_ratio_floor = "0.3"

[strategies.zen_geometer]
enabled = true
min_net_profit_usd = "5.0"

[alerting]
min_profit_threshold = "5.0"
max_revert_rate = "0.1"
max_slippage_percent = "0.05"

[aetheric_resonance_engine]
min_resonance_score = "0.0"

[scoring]
quality_ratio_floor = "0.3"
risk_aversion_k = "0.5"
regime_multiplier_retail_fomo = "1.2"
regime_multiplier_high_vol = "0.8"
regime_multiplier_calm = "1.0"
regime_multiplier_gas_war_penalty = "0.5"
temporal_harmonics_weight = "0.33"
geometric_score_weight = "0.33"
network_resonance_weight = "0.34"

[manifold]
worthy_assets = ["WETH", "USDC", "DEGEN"]

[bridges]
routes = []

[[cex]]
name = "coinbase"
ws_url = "wss://ws-feed.exchange.coinbase.com"

[chainlink_feeds]

# Base Sepolia Testnet Configuration
[chains.84532]
name = "Base Sepolia"
enabled = true
native_currency = "ETH"
max_gas_price = 50000000000  # 50 gwei

[[chains.84532.rpc_endpoints]]
url = "https://sepolia.base.org"
priority = 0

[chains.84532.tokens]
USDC = "******************************************"  # Base Sepolia USDC
WETH = "******************************************"  # Base Sepolia WETH

[chains.84532.dex]
uniswap_v3_factory = "******************************************"
uniswap_v3_router = "******************************************"

[chains.84532.contracts]
multicall = "******************************************"
# DEPLOYED TESTNET CONTRACTS
stargate_compass_v1 = "******************************************"  # DEPLOYED!
mock_aave_provider = "******************************************"
mock_stargate_router = "******************************************"
mock_aave_pool = "******************************************"