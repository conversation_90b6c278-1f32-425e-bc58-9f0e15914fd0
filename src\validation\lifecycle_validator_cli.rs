// src/validation/lifecycle_validator_cli.rs

//! CLI interface for the lifecycle validator

use crate::validation::lifecycle_validator::{
    LifecycleValidator, LifecycleValidationConfig, LifecycleValidationMetrics
};
use crate::validation::{ValidationFrameworkResult, TestDataProvider};
use clap::{Args, Subcommand};
use std::time::Duration;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tracing::{info, error};

/// CLI arguments for lifecycle validation
#[derive(Debug, Args)]
pub struct LifecycleValidationArgs {
    #[command(subcommand)]
    pub command: LifecycleValidationCommand,
}

/// Lifecycle validation commands
#[derive(Debug, Subcommand)]
pub enum LifecycleValidationCommand {
    /// Run complete trading workflow validation
    CompleteWorkflow {
        /// Number of test scenarios to run
        #[arg(long, default_value = "5")]
        scenario_count: usize,
        /// Maximum lifecycle duration in seconds
        #[arg(long, default_value = "30")]
        max_duration: u64,
        /// Enable cross-chain testing
        #[arg(long)]
        enable_cross_chain: bool,
        /// Enable concurrent strategy testing
        #[arg(long)]
        enable_concurrent: bool,
    },
    /// Run multi-strategy concurrent execution validation
    MultiStrategy {
        /// Number of concurrent strategies
        #[arg(long, default_value = "3")]
        strategy_count: usize,
        /// Test duration in seconds
        #[arg(long, default_value = "60")]
        duration: u64,
    },
    /// Run market adaptation validation
    MarketAdaptation {
        /// Test duration in seconds
        #[arg(long, default_value = "120")]
        duration: u64,
        /// Number of regime changes to test
        #[arg(long, default_value = "2")]
        regime_changes: usize,
    },
    /// Run system resilience validation
    SystemResilience {
        /// Failure injection rate (0.0 to 1.0)
        #[arg(long, default_value = "0.1")]
        failure_rate: f64,
        /// Test duration in seconds
        #[arg(long, default_value = "300")]
        duration: u64,
    },
    /// Run comprehensive lifecycle validation suite
    ComprehensiveSuite {
        /// Enable all validation types
        #[arg(long)]
        enable_all: bool,
        /// Quick test mode (reduced duration)
        #[arg(long)]
        quick: bool,
    },
    /// Show lifecycle validation demo
    Demo,
}

/// Handle lifecycle validation command
pub async fn handle_lifecycle_validation_command(
    args: LifecycleValidationArgs,
) -> ValidationFrameworkResult<()> {
    match args.command {
        LifecycleValidationCommand::CompleteWorkflow {
            scenario_count,
            max_duration,
            enable_cross_chain,
            enable_concurrent,
        } => {
            run_complete_workflow_validation(
                scenario_count,
                max_duration,
                enable_cross_chain,
                enable_concurrent,
            ).await
        }
        LifecycleValidationCommand::MultiStrategy {
            strategy_count,
            duration,
        } => {
            run_multi_strategy_validation(strategy_count, duration).await
        }
        LifecycleValidationCommand::MarketAdaptation {
            duration,
            regime_changes,
        } => {
            run_market_adaptation_validation(duration, regime_changes).await
        }
        LifecycleValidationCommand::SystemResilience {
            failure_rate,
            duration,
        } => {
            run_system_resilience_validation(failure_rate, duration).await
        }
        LifecycleValidationCommand::ComprehensiveSuite {
            enable_all,
            quick,
        } => {
            run_comprehensive_lifecycle_validation_suite(enable_all, quick).await
        }
        LifecycleValidationCommand::Demo => {
            run_lifecycle_validation_demo().await
        }
    }
}

/// Run complete workflow validation
pub async fn run_complete_workflow_validation(
    scenario_count: usize,
    max_duration: u64,
    enable_cross_chain: bool,
    enable_concurrent: bool,
) -> ValidationFrameworkResult<()> {
    info!("🔄 Starting complete trading workflow validation");
    info!("📊 Test parameters:");
    info!("   • Scenario count: {}", scenario_count);
    info!("   • Max duration: {}s", max_duration);
    info!("   • Cross-chain: {}", enable_cross_chain);
    info!("   • Concurrent strategies: {}", enable_concurrent);

    let mut config = LifecycleValidationConfig::default();
    config.max_lifecycle_duration = Duration::from_secs(max_duration);
    config.enable_cross_chain_testing = enable_cross_chain;
    config.enable_concurrent_strategy_testing = enable_concurrent;

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    // Generate test scenarios
    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let test_scenarios = scenarios.into_iter().take(scenario_count).collect::<Vec<_>>();

    info!("🧪 Generated {} test scenarios", test_scenarios.len());

    // Run validation
    let metrics = validator.validate_complete_trading_workflow(&test_scenarios).await?;

    // Display results
    display_lifecycle_metrics(&metrics);

    info!("✅ Complete workflow validation completed successfully");
    Ok(())
}

/// Run multi-strategy validation
pub async fn run_multi_strategy_validation(
    strategy_count: usize,
    duration: u64,
) -> ValidationFrameworkResult<()> {
    info!("🔄 Starting multi-strategy concurrent execution validation");
    info!("📊 Test parameters:");
    info!("   • Concurrent strategies: {}", strategy_count);
    info!("   • Test duration: {}s", duration);

    let mut config = LifecycleValidationConfig::default();
    config.concurrent_strategy_count = strategy_count;
    config.adaptation_test_duration = Duration::from_secs(duration);
    config.enable_concurrent_strategy_testing = true;

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    // Generate test scenarios
    let scenarios = test_data_provider.generate_scenario_suite().await?;

    // Run multi-strategy validation
    let metrics = validator.validate_multi_strategy_concurrent_execution(&scenarios).await?;

    // Display results
    display_multi_strategy_metrics(&metrics);

    info!("✅ Multi-strategy validation completed successfully");
    Ok(())
}

/// Run market adaptation validation
pub async fn run_market_adaptation_validation(
    duration: u64,
    regime_changes: usize,
) -> ValidationFrameworkResult<()> {
    info!("🔄 Starting market condition adaptation validation");
    info!("📊 Test parameters:");
    info!("   • Test duration: {}s", duration);
    info!("   • Regime changes: {}", regime_changes);

    let mut config = LifecycleValidationConfig::default();
    config.adaptation_test_duration = Duration::from_secs(duration);

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    // Generate initial scenario
    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let initial_scenario = &scenarios[0];

    // Run adaptation validation
    let metrics = validator.validate_market_condition_adaptation(initial_scenario).await?;

    // Display results
    display_market_adaptation_metrics(&metrics);

    info!("✅ Market adaptation validation completed successfully");
    Ok(())
}

/// Run system resilience validation
pub async fn run_system_resilience_validation(
    failure_rate: f64,
    duration: u64,
) -> ValidationFrameworkResult<()> {
    info!("🔄 Starting system resilience and recovery validation");
    info!("📊 Test parameters:");
    info!("   • Failure injection rate: {:.1}%", failure_rate * 100.0);
    info!("   • Test duration: {}s", duration);

    let mut config = LifecycleValidationConfig::default();
    config.failure_injection_rate = failure_rate;
    config.adaptation_test_duration = Duration::from_secs(duration);

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    // Generate test scenarios
    let scenarios = test_data_provider.generate_scenario_suite().await?;

    // Run resilience validation
    let metrics = validator.validate_system_resilience_and_recovery(&scenarios).await?;

    // Display results
    display_resilience_metrics(&metrics);

    info!("✅ System resilience validation completed successfully");
    Ok(())
}

/// Run comprehensive lifecycle validation suite
pub async fn run_comprehensive_lifecycle_validation_suite(
    enable_all: bool,
    quick: bool,
) -> ValidationFrameworkResult<()> {
    info!("🔄 Starting comprehensive lifecycle validation suite");
    info!("📊 Test parameters:");
    info!("   • Enable all tests: {}", enable_all);
    info!("   • Quick mode: {}", quick);

    let mut config = LifecycleValidationConfig::default();

    if quick {
        config.max_lifecycle_duration = Duration::from_secs(10);
        config.adaptation_test_duration = Duration::from_secs(30);
        config.concurrent_strategy_count = 2;
    }

    if enable_all {
        config.enable_cross_chain_testing = true;
        config.enable_concurrent_strategy_testing = true;
    }

    let enable_concurrent_testing = config.enable_concurrent_strategy_testing;
    let enable_cross_chain_testing = config.enable_cross_chain_testing;

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    // Generate comprehensive test scenarios
    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let scenario_count = if quick { 3 } else { scenarios.len() };
    let test_scenarios = scenarios.into_iter().take(scenario_count).collect::<Vec<_>>();

    info!("🧪 Running comprehensive validation with {} scenarios", test_scenarios.len());

    // Run all validation types
    info!("1️⃣ Running complete workflow validation...");
    let workflow_metrics = validator.validate_complete_trading_workflow(&test_scenarios).await?;

    if enable_concurrent_testing {
        info!("2️⃣ Running multi-strategy validation...");
        let multi_strategy_metrics = validator.validate_multi_strategy_concurrent_execution(&test_scenarios).await?;
        display_multi_strategy_metrics(&multi_strategy_metrics);
    }

    info!("3️⃣ Running market adaptation validation...");
    let adaptation_metrics = validator.validate_market_condition_adaptation(&test_scenarios[0]).await?;
    display_market_adaptation_metrics(&adaptation_metrics);

    info!("4️⃣ Running system resilience validation...");
    let resilience_metrics = validator.validate_system_resilience_and_recovery(&test_scenarios).await?;
    display_resilience_metrics(&resilience_metrics);

    // Display comprehensive results
    display_lifecycle_metrics(&workflow_metrics);

    info!("✅ Comprehensive lifecycle validation suite completed successfully");
    Ok(())
}

/// Run lifecycle validation demo
pub async fn run_lifecycle_validation_demo() -> ValidationFrameworkResult<()> {
    info!("🎬 Starting lifecycle validation demo");

    let config = LifecycleValidationConfig {
        max_lifecycle_duration: Duration::from_secs(5),
        max_detection_time: Duration::from_millis(50),
        max_scoring_time: Duration::from_millis(25),
        max_execution_time: Duration::from_secs(2),
        max_settlement_time: Duration::from_secs(2),
        min_profit_threshold_usd: dec!(1.0),
        concurrent_strategy_count: 2,
        adaptation_test_duration: Duration::from_secs(10),
        failure_injection_rate: 0.2,
        enable_cross_chain_testing: true,
        enable_concurrent_strategy_testing: true,
    };

    let validator = LifecycleValidator::new(config)?;
    let test_data_provider = TestDataProvider::new()?;

    info!("📊 Demo Configuration:");
    info!("   • Max lifecycle duration: 5s");
    info!("   • Detection timeout: 50ms");
    info!("   • Scoring timeout: 25ms");
    info!("   • Execution timeout: 2s");
    info!("   • Settlement timeout: 2s");
    info!("   • Min profit threshold: $1.00");
    info!("   • Concurrent strategies: 2");
    info!("   • Cross-chain enabled: Yes");

    // Generate demo scenarios
    let scenarios = test_data_provider.generate_scenario_suite().await?;
    let demo_scenarios = scenarios.into_iter().take(3).collect::<Vec<_>>();

    info!("🧪 Generated {} demo scenarios", demo_scenarios.len());

    // Run demo validation
    info!("🔄 Running demo lifecycle validation...");
    let metrics = validator.validate_complete_trading_workflow(&demo_scenarios).await?;

    // Display demo results
    display_lifecycle_metrics(&metrics);

    info!("🎉 Lifecycle validation demo completed successfully!");
    show_lifecycle_validation_usage_examples();

    Ok(())
}

/// Display lifecycle metrics
fn display_lifecycle_metrics(metrics: &LifecycleValidationMetrics) {
    info!("📈 Lifecycle Validation Results:");
    info!("   • Total lifecycles tested: {}", metrics.total_lifecycles_tested);
    info!("   • Successful lifecycles: {}", metrics.successful_lifecycles);
    info!("   • Failed lifecycles: {}", metrics.failed_lifecycles);
    info!("   • Success rate: {:.1}%", 
          (metrics.successful_lifecycles as f64 / metrics.total_lifecycles_tested as f64) * 100.0);
    info!("   • Average lifecycle time: {}ms", metrics.average_lifecycle_time.as_millis());

    info!("🔧 Pipeline Performance:");
    info!("   • Detection success rate: {:.1}%", metrics.pipeline_performance.detection_performance.success_rate * 100.0);
    info!("   • Scoring success rate: {:.1}%", metrics.pipeline_performance.scoring_performance.success_rate * 100.0);
    info!("   • Execution success rate: {:.1}%", metrics.pipeline_performance.execution_performance.success_rate * 100.0);
    info!("   • Settlement success rate: {:.1}%", metrics.pipeline_performance.settlement_performance.success_rate * 100.0);
    info!("   • Overall pipeline efficiency: {:.1}%", metrics.pipeline_performance.pipeline_efficiency * 100.0);

    info!("💰 Profit Realization:");
    info!("   • Total net profit: ${:.2}", metrics.profit_realization.total_net_profit_usd);
    info!("   • Average profit per opportunity: ${:.2}", metrics.profit_realization.average_profit_per_opportunity);
    info!("   • Profit realization rate: {:.1}%", metrics.profit_realization.profit_realization_rate * 100.0);
    info!("   • Cost accuracy: {:.1}%", metrics.profit_realization.cost_accuracy * 100.0);
}

/// Display multi-strategy metrics
fn display_multi_strategy_metrics(metrics: &crate::validation::lifecycle_validator::MultiStrategyPerformanceMetrics) {
    info!("🔀 Multi-Strategy Performance:");
    info!("   • Concurrent strategies tested: {}", metrics.concurrent_strategies_tested);
    info!("   • Coordination success rate: {:.1}%", metrics.coordination_success_rate * 100.0);
    info!("   • Resource contention incidents: {}", metrics.resource_contention_incidents);
    
    for (strategy_name, performance) in &metrics.individual_strategy_performance {
        info!("   • {}: {:.1}% success, ${:.2} avg profit", 
              strategy_name, 
              performance.success_rate * 100.0, 
              performance.average_profit_usd);
    }
}

/// Display market adaptation metrics
fn display_market_adaptation_metrics(metrics: &crate::validation::lifecycle_validator::MarketAdaptationPerformanceMetrics) {
    info!("🌊 Market Adaptation Performance:");
    info!("   • Regime changes detected: {}", metrics.regime_changes_detected);
    info!("   • Successful adaptations: {}", metrics.successful_adaptations);
    info!("   • Adaptation success rate: {:.1}%", 
          (metrics.successful_adaptations as f64 / metrics.regime_changes_detected as f64) * 100.0);
    info!("   • Average adaptation time: {}ms", metrics.average_adaptation_time.as_millis());
    info!("   • Parameter adjustment accuracy: {:.1}%", metrics.parameter_adjustment_accuracy * 100.0);
}

/// Display resilience metrics
fn display_resilience_metrics(metrics: &crate::validation::lifecycle_validator::ResiliencePerformanceMetrics) {
    info!("🛡️ System Resilience Performance:");
    info!("   • Failures injected: {}", metrics.failures_injected);
    info!("   • Successful recoveries: {}", metrics.successful_recoveries);
    info!("   • Recovery success rate: {:.1}%", 
          (metrics.successful_recoveries as f64 / metrics.failures_injected as f64) * 100.0);
    info!("   • Average recovery time: {}ms", metrics.average_recovery_time.as_millis());
    info!("   • System availability: {:.2}%", metrics.system_availability * 100.0);
}

/// Show lifecycle validation usage examples
pub fn show_lifecycle_validation_usage_examples() {
    info!("📚 Lifecycle Validation Usage Examples:");
    info!("");
    info!("# Complete workflow validation:");
    info!("cargo run -- validation lifecycle complete-workflow --scenario-count 10 --max-duration 60 --enable-cross-chain --enable-concurrent");
    info!("");
    info!("# Multi-strategy testing:");
    info!("cargo run -- validation lifecycle multi-strategy --strategy-count 5 --duration 120");
    info!("");
    info!("# Market adaptation testing:");
    info!("cargo run -- validation lifecycle market-adaptation --duration 300 --regime-changes 3");
    info!("");
    info!("# System resilience testing:");
    info!("cargo run -- validation lifecycle system-resilience --failure-rate 0.15 --duration 600");
    info!("");
    info!("# Comprehensive validation suite:");
    info!("cargo run -- validation lifecycle comprehensive-suite --enable-all");
    info!("");
    info!("# Quick comprehensive test:");
    info!("cargo run -- validation lifecycle comprehensive-suite --quick");
    info!("");
    info!("# Demo mode:");
    info!("cargo run -- validation lifecycle demo");
}