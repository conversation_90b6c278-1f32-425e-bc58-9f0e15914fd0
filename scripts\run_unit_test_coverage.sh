#!/bin/bash
# scripts/run_unit_test_coverage.sh
# Unit test coverage analysis for Basilisk Bot (no external dependencies)

set -e

echo "🧹 Cleaning previous artifacts..."
cargo clean

echo "🔧 Setting coverage flags..."
export CARGO_INCREMENTAL=0
export RUSTFLAGS="-Cinstrument-coverage"
export LLVM_PROFILE_FILE="basilisk_bot-%p-%m.profraw"

echo "🧪 Running ONLY unit tests (no private keys or network access required)..."
# Run only library unit tests - these are completely self-contained
cargo test --lib --all-features

echo "📊 Generating coverage report..."
grcov . --binary-path ./target/debug/ -s . -t html --branch --ignore-not-existing -o ./coverage/

echo "✅ Unit test coverage report generated in ./coverage/index.html"
echo "🌐 Open ./coverage/index.html in your browser to view the report"

# Optional: Generate additional formats
echo "📋 Generating text summary..."
grcov . --binary-path ./target/debug/ -s . -t lcov --branch --ignore-not-existing -o ./coverage/lcov.info

echo "📈 Unit test coverage analysis complete!"
echo "   - HTML Report: ./coverage/index.html"
echo "   - LCOV Data:   ./coverage/lcov.info"
echo ""
echo "ℹ️  This report covers only unit tests (no integration tests requiring external dependencies)"
