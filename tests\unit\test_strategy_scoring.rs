// Unit Tests for Strategy Scoring Engine
// Tests opportunity scoring algorithms, pillar integration, and scoring logic

use basilisk_bot::strategies::scoring::ScoringEngine;
use basilisk_bot::config::ScoringConfig;
use basilisk_bot::shared_types::*;
use basilisk_bot::math::scoring::DummyGeometricScorer;
use ethers::types::Address;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use mockall::predicate::*;
use mockall::mock;
use proptest::prelude::*;
use pretty_assertions::assert_eq;
use std::sync::Arc;
use std::collections::HashMap;
use tokio_test;

// Mock GeometricScorer for testing
mock! {
    TestGeometricScorer {}

    #[async_trait::async_trait]
    impl GeometricScorer for TestGeometricScorer {
        async fn calculate_score(&self, path: &ArbitragePath) -> anyhow::Result<GeometricScore>;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> ScoringConfig {
        ScoringConfig {
            quality_ratio_floor: dec!(0.3),
            risk_aversion_k: dec!(0.5),
            temporal_weight: dec!(0.3),
            geometric_weight: dec!(0.4),
            network_weight: dec!(0.3),
            regime_multipliers: {
                let mut map = HashMap::new();
                map.insert(MarketRegime::CalmOrderly, dec!(1.0));
                map.insert(MarketRegime::RetailFomoSpike, dec!(1.2));
                map.insert(MarketRegime::BotGasWar, dec!(0.8));
                map.insert(MarketRegime::HighVolatilityCorrection, dec!(0.6));
                map.insert(MarketRegime::Trending, dec!(1.1));
                map.insert(MarketRegime::Unknown, dec!(0.9));
                map
            },
        }
    }

    fn create_test_opportunity() -> Opportunity {
        Opportunity::DexArbitrage {
            base: OpportunityBase {
                id: "test_opp_1".to_string(),
                source_scanner: "test_scanner".to_string(),
                estimated_gross_profit_usd: dec!(100.0),
                associated_volatility: dec!(0.1),
                requires_flash_liquidity: false,
                chain_id: 1,
                timestamp: 1640995200,
                intersection_value_usd: dec!(50.0), // 50% quality ratio
                aetheric_resonance_score: None,
            },
            data: DexArbitrageData {
                path: vec![
                    ArbitragePool {
                        address: Address::random(),
                        token0_symbol: "WETH".to_string(),
                        token1_symbol: "USDC".to_string(),
                        reserve0: dec!(1000.0),
                        reserve1: dec!(2000000.0),
                        protocol: "uniswap".to_string(),
                    },
                    ArbitragePool {
                        address: Address::random(),
                        token0_symbol: "USDC".to_string(),
                        token1_symbol: "DAI".to_string(),
                        reserve0: dec!(2000000.0),
                        reserve1: dec!(2000000.0),
                        protocol: "sushiswap".to_string(),
                    },
                ],
                amount_in: dec!(10.0),
                expected_amount_out: dec!(10.05),
            },
        }
    }

    #[tokio::test]
    async fn test_scoring_engine_creation() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        
        let engine = ScoringEngine::new(config.clone(), geometric_scorer);
        
        assert_eq!(engine.config.quality_ratio_floor, dec!(0.3));
        assert_eq!(engine.config.risk_aversion_k, dec!(0.5));
    }

    #[tokio::test]
    async fn test_quality_ratio_floor_rejection() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        // Create opportunity with low quality ratio (below 0.3 floor)
        let mut opportunity = create_test_opportunity();
        opportunity.base_mut().intersection_value_usd = dec!(20.0); // 20% quality ratio
        
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let network_resonance = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        let score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &centrality_scores,
        ).await;
        
        assert_eq!(score, dec!(-1.0)); // Should be rejected
    }

    #[tokio::test]
    async fn test_quality_ratio_floor_acceptance() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        let opportunity = create_test_opportunity(); // 50% quality ratio (above floor)
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let network_resonance = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        let score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &centrality_scores,
        ).await;
        
        assert!(score > dec!(0.0)); // Should be accepted
    }

    #[tokio::test]
    async fn test_regime_multiplier_effects() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        let opportunity = create_test_opportunity();
        let temporal_harmonics = None;
        let network_resonance = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        // Test different regimes
        let regimes = vec![
            (MarketRegime::CalmOrderly, dec!(1.0)),
            (MarketRegime::RetailFomoSpike, dec!(1.2)),
            (MarketRegime::BotGasWar, dec!(0.8)),
            (MarketRegime::HighVolatilityCorrection, dec!(0.6)),
        ];
        
        let mut scores = Vec::new();
        for (regime, expected_multiplier) in regimes {
            let score = engine.calculate_opportunity_score(
                &opportunity,
                &regime,
                &temporal_harmonics,
                &network_resonance,
                &centrality_scores,
            ).await;
            scores.push((regime, score, expected_multiplier));
        }
        
        // Verify that higher multipliers result in higher scores
        let calm_score = scores.iter().find(|(r, _, _)| *r == MarketRegime::CalmOrderly).unwrap().1;
        let fomo_score = scores.iter().find(|(r, _, _)| *r == MarketRegime::RetailFomoSpike).unwrap().1;
        let gas_war_score = scores.iter().find(|(r, _, _)| *r == MarketRegime::BotGasWar).unwrap().1;
        
        assert!(fomo_score > calm_score); // 1.2x multiplier should be higher than 1.0x
        assert!(calm_score > gas_war_score); // 1.0x multiplier should be higher than 0.8x
    }

    #[tokio::test]
    async fn test_risk_adjustment_calculation() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        // Create two opportunities with different volatilities
        let mut low_vol_opp = create_test_opportunity();
        low_vol_opp.base_mut().associated_volatility = dec!(0.05); // 5% volatility
        
        let mut high_vol_opp = create_test_opportunity();
        high_vol_opp.base_mut().associated_volatility = dec!(0.2); // 20% volatility
        
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let network_resonance = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        let low_vol_score = engine.calculate_opportunity_score(
            &low_vol_opp,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &centrality_scores,
        ).await;
        
        let high_vol_score = engine.calculate_opportunity_score(
            &high_vol_opp,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &centrality_scores,
        ).await;
        
        // Lower volatility should result in higher score (less risk adjustment)
        assert!(low_vol_score > high_vol_score);
    }

    #[tokio::test]
    async fn test_temporal_harmonics_scoring() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        let opportunity = create_test_opportunity();
        let market_regime = MarketRegime::CalmOrderly;
        let centrality_scores = Arc::new(HashMap::new());
        
        // Test with high stability temporal harmonics
        let high_stability_harmonics = Some(TemporalHarmonics {
            dominant_cycles_minutes: vec![(15.0, 0.8), (30.0, 0.7)],
            market_rhythm_stability: 0.9,
        });
        
        // Test with low stability temporal harmonics
        let low_stability_harmonics = Some(TemporalHarmonics {
            dominant_cycles_minutes: vec![(15.0, 0.3), (30.0, 0.2)],
            market_rhythm_stability: 0.2,
        });
        
        let high_stability_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &high_stability_harmonics,
            &None,
            &centrality_scores,
        ).await;
        
        let low_stability_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &low_stability_harmonics,
            &None,
            &centrality_scores,
        ).await;
        
        // Higher stability should result in higher score
        assert!(high_stability_score > low_stability_score);
    }

    #[tokio::test]
    async fn test_network_resonance_scoring() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        let opportunity = create_test_opportunity();
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        // Test with healthy network state
        let healthy_network = Some(NetworkResonanceState {
            sp_time_ms: 10.0,
            network_coherence_score: 0.9,
            is_shock_event: false,
            sp_time_20th_percentile: 12.0,
            sequencer_status: "Healthy".to_string(),
            censorship_detected: false,
        });
        
        // Test with degraded network state
        let degraded_network = Some(NetworkResonanceState {
            sp_time_ms: 50.0,
            network_coherence_score: 0.3,
            is_shock_event: true,
            sp_time_20th_percentile: 12.0,
            sequencer_status: "Degraded".to_string(),
            censorship_detected: true,
        });
        
        let healthy_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &healthy_network,
            &centrality_scores,
        ).await;
        
        let degraded_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &degraded_network,
            &centrality_scores,
        ).await;
        
        // Healthy network should result in higher score
        assert!(healthy_score > degraded_score);
    }

    #[tokio::test]
    async fn test_geometric_scoring_with_mock() {
        let config = create_test_config();
        let mut mock_scorer = MockTestGeometricScorer::new();
        
        // Set up mock expectations
        mock_scorer
            .expect_calculate_score()
            .returning(|_| Ok(GeometricScore {
                convexity_ratio: dec!(0.8),
                liquidity_centroid_bias: dec!(0.7),
                harmonic_path_score: dec!(0.9),
                vesica_piscis_depth: dec!(0.85),
            }));
        
        let engine = ScoringEngine::new(config, Arc::new(mock_scorer));
        
        let opportunity = create_test_opportunity();
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let network_resonance = None;
        let centrality_scores = Arc::new(HashMap::new());
        
        let score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &centrality_scores,
        ).await;
        
        assert!(score > dec!(0.0));
    }

    #[tokio::test]
    async fn test_centrality_scoring() {
        let config = create_test_config();
        let geometric_scorer = Arc::new(DummyGeometricScorer);
        let engine = ScoringEngine::new(config, geometric_scorer);
        
        let opportunity = create_test_opportunity();
        let market_regime = MarketRegime::CalmOrderly;
        let temporal_harmonics = None;
        let network_resonance = None;
        
        // Test with high centrality scores
        let mut high_centrality = HashMap::new();
        high_centrality.insert("WETH".to_string(), dec!(0.9));
        high_centrality.insert("USDC".to_string(), dec!(0.8));
        let high_centrality_scores = Arc::new(high_centrality);
        
        // Test with low centrality scores
        let mut low_centrality = HashMap::new();
        low_centrality.insert("WETH".to_string(), dec!(0.2));
        low_centrality.insert("USDC".to_string(), dec!(0.1));
        let low_centrality_scores = Arc::new(low_centrality);
        
        let high_centrality_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &high_centrality_scores,
        ).await;
        
        let low_centrality_score = engine.calculate_opportunity_score(
            &opportunity,
            &market_regime,
            &temporal_harmonics,
            &network_resonance,
            &low_centrality_scores,
        ).await;
        
        // Higher centrality should result in higher score
        assert!(high_centrality_score > low_centrality_score);
    }

    #[test]
    fn test_scoring_config_validation() {
        let config = create_test_config();
        
        // Verify weights are reasonable
        assert!(config.temporal_weight >= dec!(0.0));
        assert!(config.temporal_weight <= dec!(1.0));
        assert!(config.geometric_weight >= dec!(0.0));
        assert!(config.geometric_weight <= dec!(1.0));
        assert!(config.network_weight >= dec!(0.0));
        assert!(config.network_weight <= dec!(1.0));
        
        // Verify quality ratio floor is reasonable
        assert!(config.quality_ratio_floor >= dec!(0.0));
        assert!(config.quality_ratio_floor <= dec!(1.0));
        
        // Verify risk aversion is reasonable
        assert!(config.risk_aversion_k >= dec!(0.0));
        assert!(config.risk_aversion_k <= dec!(1.0));
    }
}
