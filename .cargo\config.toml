# Cargo configuration for better memory usage during compilation

[build]
# Use faster linker to reduce memory usage and keep coverage instrumentation
rustflags = [
    "-C", "instrument-coverage",    # Keep existing coverage instrumentation
    "-C", "link-arg=-fuse-ld=lld",  # Use LLD linker (faster, less memory)
    "-C", "target-cpu=native",      # Optimize for current CPU
]

# Alternative linker configurations (uncomment if lld is not available)
# For systems with mold linker (even faster):
# rustflags = ["-C", "instrument-coverage", "-C", "link-arg=-fuse-ld=mold"]

[target.x86_64-unknown-linux-gnu]
# Use LLD linker specifically for Linux builds
linker = "clang"
rustflags = [
    "-C", "instrument-coverage",
    "-C", "link-arg=-fuse-ld=lld"
]
