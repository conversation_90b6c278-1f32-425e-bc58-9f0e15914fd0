#!/bin/bash
# scripts/run_coverage.sh
# Automated test coverage analysis for Basilisk Bot

set -e

echo "🧹 Cleaning previous artifacts..."
cargo clean

echo "🔧 Setting coverage flags..."
export CARGO_INCREMENTAL=0
export RUSTFLAGS="-Cinstrument-coverage"
export LLVM_PROFILE_FILE="basilisk_bot-%p-%m.profraw"

echo "🧪 Running unit tests (no external dependencies required)..."
# Run only unit tests to avoid needing private keys or network access
cargo test --lib --all-features

echo "📊 Generating coverage report..."
grcov . --binary-path ./target/debug/ -s . -t html --branch --ignore-not-existing -o ./coverage/

echo "✅ Coverage report generated in ./coverage/index.html"
echo "🌐 Open ./coverage/index.html in your browser to view the report"

# Optional: Generate additional formats
echo "📋 Generating text summary..."
grcov . --binary-path ./target/debug/ -s . -t lcov --branch --ignore-not-existing -o ./coverage/lcov.info

echo "📈 Coverage analysis complete!"
echo "   - HTML Report: ./coverage/index.html"
echo "   - LCOV Data:   ./coverage/lcov.info"
