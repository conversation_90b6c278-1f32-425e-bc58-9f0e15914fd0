// MISSION: Nomad<PERSON> Hunter - The Migration Controller
// WHY: Autonomous cross-chain opportunity density analysis and migration decisions
// HOW: Configuration-driven cost modeling with real-time NATS metric ingestion

use crate::config::{BridgeRoute, ChainConfig, Settings, ScannerSettings, ScoringConfig, ContractAddresses, DexConfig, TokenConfig, StrategyConfig, ExecutionConfig, Secrets, NatsConfig};
use crate::shared_types::NatsTopics;
use async_nats::{Client, Subscriber};
use futures::StreamExt;
use dashmap::DashMap;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::{interval, Instant};
use tracing::{debug, error, info, warn};
use crate::error::BasiliskError;

/// Chain metrics for migration decision making
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainMetrics {
    pub chain_id: u64,
    pub opportunity_density: Decimal,      // Opportunities per hour
    pub avg_profit_usd: Decimal,           // Average profit per opportunity
    pub gas_cost_gwei: Decimal,            // Current gas cost
    pub success_rate: Decimal,             // Success rate (0.0 to 1.0)
    pub last_updated: u64,                 // Unix timestamp
}

/// Migration decision result
#[derive(Debug, Clone, PartialEq)]
pub enum MigrationDecision {
    Stay,
    Migrate { target_chain: u64, expected_improvement: Decimal },
}

/// Opportunity metrics from NATS for chain analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpportunityMetric {
    pub chain_id: u64,
    pub profit_usd: Decimal,
    pub gas_cost_usd: Decimal,
    pub success: bool,
    pub timestamp: u64,
}

/// The Nomadic Hunter - Migration Controller
pub struct MigrationController {
    config: Arc<Settings>,
    nats_client: Client,
    chain_metrics: Arc<DashMap<u64, ChainMetrics>>,
    current_chain: u64,
    migration_threshold_multiplier: Decimal,
    data_staleness_threshold: Duration,
}

impl MigrationController {
    /// Create a new Migration Controller
    pub fn new(config: Arc<Settings>, nats_client: Client) -> Self {
        let current_chain = 8453; // Default to Base chain
        
        Self {
            config,
            nats_client,
            chain_metrics: Arc::new(DashMap::new()),
            current_chain,
            migration_threshold_multiplier: Decimal::new(12, 1), // 1.2x improvement required
            data_staleness_threshold: Duration::from_secs(300), // 5 minutes
        }
    }

    /// Start the migration controller
    pub async fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        info!("NOMADIC HUNTER: Starting Migration Controller on chain {}", self.current_chain);

        // Clone necessary data for the async tasks
        let nats_client = self.nats_client.clone();
        let chain_metrics = self.chain_metrics.clone();
        
        // Start listening for metrics
        let metrics_task = Self::listen_for_metrics_static(nats_client, chain_metrics);
        
        // Start decision loop
        let mut decision_interval = interval(Duration::from_secs(60)); // Check every minute
        
        tokio::select! {
            result = metrics_task => {
                error!("NOMADIC HUNTER: Metrics listener terminated: {:?}", result);
                result
            }
            _ = async {
                loop {
                    decision_interval.tick().await;
                    if let Err(e) = self.make_decision_cycle().await {
                        error!("NOMADIC HUNTER: Decision cycle error: {:?}", e);
                    }
                }
            } => {
                Ok(())
            }
        }
    }

    /// Listen for opportunity metrics from NATS (static version)
    async fn listen_for_metrics_static(
        nats_client: async_nats::Client,
        chain_metrics: Arc<DashMap<u64, ChainMetrics>>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let subject = "zen_geometer.opportunity.analyzed.*";
        let mut subscriber = nats_client.subscribe(subject).await?;
        
        info!("NOMADIC HUNTER: Listening for metrics on subject: {}", subject);
        
        while let Some(message) = subscriber.next().await {
            if let Err(e) = Self::process_metric_message_static(message, &chain_metrics).await {
                warn!("NOMADIC HUNTER: Failed to process metric message: {:?}", e);
            }
        }
        
        Ok(())
    }

    /// Listen for opportunity metrics from NATS
    async fn listen_for_metrics(&self) -> Result<(), Box<dyn std::error::Error>> {
        let subject = "zen_geometer.opportunity.analyzed.*";
        let mut subscriber = self.nats_client.subscribe(subject).await?;
        
        info!("NOMADIC HUNTER: Listening for metrics on subject: {}", subject);
        
        while let Some(message) = subscriber.next().await {
            if let Err(e) = self.process_metric_message(message).await {
                warn!("NOMADIC HUNTER: Failed to process metric message: {:?}", e);
            }
        }
        
        Ok(())
    }

    /// Process incoming metric message (static version)
    async fn process_metric_message_static(
        message: async_nats::Message,
        chain_metrics: &Arc<DashMap<u64, ChainMetrics>>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Extract chain ID from subject (zen_geometer.opportunity.analyzed.{chain_id})
        let subject_parts: Vec<&str> = message.subject.split('.').collect();
        if subject_parts.len() != 4 {
            warn!("NOMADIC HUNTER: Invalid subject format: {}", message.subject);
            return Ok(());
        }

        let chain_id: u64 = subject_parts[3].parse().map_err(|e| {
            warn!("NOMADIC HUNTER: Invalid chain ID in subject: {}", subject_parts[3]);
            e
        })?;

        // Parse the opportunity metric
        let metric: OpportunityMetric = serde_json::from_slice(&message.payload)?;
        
        // Update chain metrics
        let now = SystemTime::now().duration_since(UNIX_EPOCH)
            .map_err(|e| BasiliskError::SystemTimeError(e.to_string()))?.as_secs();
        
        chain_metrics.entry(chain_id).and_modify(|existing| {
            // Update running averages
            existing.avg_profit_usd = (existing.avg_profit_usd + metric.profit_usd) / Decimal::from(2);
            existing.gas_cost_gwei = metric.gas_cost_usd; // Simplified - should convert from USD to gwei
            existing.success_rate = if metric.success { 
                (existing.success_rate + Decimal::from(1)) / Decimal::from(2)
            } else {
                existing.success_rate / Decimal::from(2)
            };
            existing.last_updated = now;
        }).or_insert(ChainMetrics {
            chain_id,
            opportunity_density: Decimal::from(1), // Start with 1
            avg_profit_usd: metric.profit_usd,
            gas_cost_gwei: metric.gas_cost_usd, // Simplified
            success_rate: if metric.success { Decimal::from(1) } else { Decimal::from(0) },
            last_updated: now,
        });

        debug!("NOMADIC HUNTER: Updated metrics for chain {}", chain_id);
        Ok(())
    }

    /// Process incoming metric message
    async fn process_metric_message(&self, message: async_nats::Message) -> Result<(), Box<dyn std::error::Error>> {
        // Extract chain ID from subject (zen_geometer.opportunity.analyzed.{chain_id})
        let subject_parts: Vec<&str> = message.subject.split('.').collect();
        if subject_parts.len() != 4 {
            warn!("NOMADIC HUNTER: Invalid subject format: {}", message.subject);
            return Ok(());
        }
        
        let chain_id: u64 = subject_parts[3].parse()?;
        
        // Verify chain is enabled in configuration
        if let Some(chain_config) = self.config.chains.get(&chain_id) {
            if !chain_config.enabled.unwrap_or(true) {
                debug!("NOMADIC HUNTER: Ignoring metrics for disabled chain {}", chain_id);
                return Ok(());
            }
        } else {
            warn!("NOMADIC HUNTER: Received metrics for unknown chain {}", chain_id);
            return Ok(());
        }
        
        // Parse the metric
        let metric: OpportunityMetric = serde_json::from_slice(&message.payload)?;
        
        // Update chain metrics
        self.update_chain_metrics(metric).await;
        
        Ok(())
    }

    /// Update chain metrics with new opportunity data
    async fn update_chain_metrics(&self, metric: OpportunityMetric) {
        let now = SystemTime::now().duration_since(UNIX_EPOCH)
            .map_err(|e| error!("NOMADIC HUNTER: System time error: {}", e)).unwrap_or_default().as_secs();
        
        self.chain_metrics.entry(metric.chain_id).and_modify(|metrics| {
            // Update running averages (simple exponential moving average)
            let alpha = Decimal::new(1, 1); // 0.1 smoothing factor
            
            metrics.avg_profit_usd = metrics.avg_profit_usd * (Decimal::ONE - alpha) + metric.profit_usd * alpha;
            metrics.opportunity_density += Decimal::ONE; // Increment opportunity count
            
            if metric.success {
                metrics.success_rate = metrics.success_rate * (Decimal::ONE - alpha) + alpha;
            } else {
                metrics.success_rate = metrics.success_rate * (Decimal::ONE - alpha);
            }
            
            metrics.last_updated = now;
            
        }).or_insert_with(|| {
            ChainMetrics {
                chain_id: metric.chain_id,
                opportunity_density: Decimal::ONE,
                avg_profit_usd: metric.profit_usd,
                gas_cost_gwei: Decimal::ZERO, // Will be updated from gas price feeds
                success_rate: if metric.success { Decimal::ONE } else { Decimal::ZERO },
                last_updated: now,
            }
        });
        
        debug!("NOMADIC HUNTER: Updated metrics for chain {}: profit=${:.2}, success_rate={:.2}", 
               metric.chain_id, metric.profit_usd, 
               self.chain_metrics.get(&metric.chain_id).map(|m| m.success_rate).unwrap_or_default());
    }

    /// Make migration decision cycle
    async fn make_decision_cycle(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let decision = self.make_decision().await;
        
        match decision {
            MigrationDecision::Stay => {
                debug!("NOMADIC HUNTER: Decision: Stay on chain {}", self.current_chain);
            }
            MigrationDecision::Migrate { target_chain, expected_improvement } => {
                info!("NOMADIC HUNTER: Decision: Migrate from {} to {} (expected improvement: {:.2}x)", 
                      self.current_chain, target_chain, expected_improvement);
                
                // Publish migration command
                self.publish_migration_command(target_chain).await?;
                
                // Update current chain
                self.current_chain = target_chain;
            }
        }
        
        Ok(())
    }

    /// Core migration decision algorithm
    async fn make_decision(&self) -> MigrationDecision {
        let current_score = self.calculate_chain_score(self.current_chain).await;
        let mut best_alternative: Option<(u64, Decimal)> = None;
        
        // Evaluate all enabled chains except current
        for chain_id in self.config.as_ref().get_enabled_chains() {
            if chain_id == self.current_chain {
                continue;
            }
            
            // Check if we have recent data for this chain
            if let Some(metrics) = self.chain_metrics.get(&chain_id) {
                let now = SystemTime::now().duration_since(UNIX_EPOCH)
                    .map_err(|e| error!("NOMADIC HUNTER: System time error: {}", e)).unwrap_or_default().as_secs();
                if now - metrics.last_updated > self.data_staleness_threshold.as_secs() {
                    debug!("NOMADIC HUNTER: Skipping chain {} due to stale data", chain_id);
                    continue;
                }
                
                let score = self.calculate_chain_score(chain_id).await;
                
                if let Some((_, best_score)) = best_alternative {
                    if score > best_score {
                        best_alternative = Some((chain_id, score));
                    }
                } else {
                    best_alternative = Some((chain_id, score));
                }
            }
        }
        
        // Apply migration threshold and cost analysis
        if let Some((target_chain, target_score)) = best_alternative {
            // Get migration cost
            let (migration_cost, _latency) = self.get_migration_cost(self.current_chain, target_chain);
            
            // Calculate improvement ratio accounting for migration cost
            let improvement_ratio = if current_score > Decimal::ZERO {
                (target_score - migration_cost) / current_score
            } else {
                target_score - migration_cost
            };
            
            if improvement_ratio >= self.migration_threshold_multiplier {
                return MigrationDecision::Migrate { 
                    target_chain, 
                    expected_improvement: improvement_ratio 
                };
            } else {
                debug!("NOMADIC HUNTER: Target chain {} improvement {:.2}x below threshold {:.2}x", 
                       target_chain, improvement_ratio, self.migration_threshold_multiplier);
            }
        }
        
        MigrationDecision::Stay
    }

    /// Calculate comprehensive score for a chain
    async fn calculate_chain_score(&self, chain_id: u64) -> Decimal {
        if let Some(metrics) = self.chain_metrics.get(&chain_id) {
            // Score = (opportunity_density * avg_profit * success_rate) - gas_costs
            let base_score = metrics.opportunity_density * metrics.avg_profit_usd * metrics.success_rate;
            let gas_penalty = metrics.gas_cost_gwei * Decimal::new(1, 2); // Convert gwei to USD penalty
            
            (base_score - gas_penalty).max(Decimal::ZERO)
        } else {
            Decimal::ZERO
        }
    }

    /// Get migration cost and latency for a route
    fn get_migration_cost(&self, from_chain: u64, to_chain: u64) -> (Decimal, Duration) {
        // Placeholder migration cost calculation - will be enhanced in migration
        let cost = rust_decimal_macros::dec!(10.0); // $10 default migration cost
        let latency = 30; // 30 seconds default latency
        if cost > rust_decimal_macros::dec!(0.0) {
            (cost, Duration::from_secs(latency as u64))
        } else {
            // Return prohibitively high cost for unconfigured routes
            warn!("NOMADIC HUNTER: No bridge route configured from {} to {}", from_chain, to_chain);
            (Decimal::MAX, Duration::MAX)
        }
    }

    /// Publish migration command to NATS
    async fn publish_migration_command(&self, target_chain: u64) -> Result<(), Box<dyn std::error::Error>> {
        let command = serde_json::json!({
            "action": "set_active_chain",
            "target_chain": target_chain,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH)
                .map_err(|e| BasiliskError::SystemTimeError(e.to_string()))?.as_secs()
        });
        
        self.nats_client
            .publish(crate::shared_types::NatsTopics::ZEN_GEOMETER_CONTROL_SET_ACTIVE_CHAIN, command.to_string().into())
            .await?;
        
        info!("NOMADIC HUNTER: Published migration command to chain {}", target_chain);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::BridgeRoute;
    use std::collections::HashMap;

    fn create_test_config() -> Arc<Settings> {
        let mut chains = HashMap::new();
        
        // Add test chains
        chains.insert(8453, ChainConfig {
            name: "Base".to_string(),
            rpc_url: Some("http://localhost:8545".to_string()),
            max_gas_price: 100,
            private_key_env_var: Some("PRIVATE_KEY".to_string()),
            contracts: ContractAddresses::default(),
            dex: DexConfig::default(),
            enabled: Some(true),
            rpc_endpoints: Some(vec![]),
            tokens: Some(TokenConfig::default()),
        });
        
        chains.insert(42161, ChainConfig {
            name: "Arbitrum".to_string(),
            rpc_url: Some("http://localhost:8546".to_string()),
            max_gas_price: 100,
            private_key_env_var: Some("PRIVATE_KEY".to_string()),
            contracts: ContractAddresses::default(),
            dex: DexConfig::default(),
            enabled: Some(true),
            rpc_endpoints: Some(vec![]),
            tokens: Some(TokenConfig::default()),
        });
        
        // Create a simple bridge config structure for testing
        let bridge_routes = vec![
            (8453, 42161, 15.0, 600),
            (42161, 8453, 15.0, 600),
        ];
        
        // Return a minimal test config that matches the actual Settings structure
        Arc::new(Settings {
            app_name: "test".to_string(),
            log_level: "info".to_string(),
            chains,
            strategy: StrategyConfig::default(),
            execution: ExecutionConfig::default(),
            secrets: Secrets::default(),
            scoring: ScoringConfig::default(),
            nats: NatsConfig::default(),
            active_chain_id: 8453,
        })
    }

    #[tokio::test]
    async fn test_migration_decision_stay() {
        let config = create_test_config();
        let nats_client = async_nats::connect("nats://localhost:4222").await
            .expect("NATS server should be available for testing");
        let controller = MigrationController::new(config, nats_client);
        
        // No metrics = stay decision
        let decision = controller.make_decision().await;
        assert_eq!(decision, MigrationDecision::Stay);
    }

    #[tokio::test]
    async fn test_migration_cost_calculation() {
        let config = create_test_config();
        let nats_client = async_nats::connect("nats://localhost:4222").await
            .expect("NATS server should be available for testing");
        let controller = MigrationController::new(config, nats_client);
        
        let (cost, latency) = controller.get_migration_cost(8453, 42161);
        assert_eq!(cost, rust_decimal_macros::dec!(10.0)); // Default cost
        assert_eq!(latency, Duration::from_secs(30)); // Default latency
    }

    #[tokio::test]
    async fn test_chain_score_calculation() {
        let config = create_test_config();
        let nats_client = async_nats::connect("nats://localhost:4222").await
            .expect("NATS server should be available for testing");
        let controller = MigrationController::new(config, nats_client);
        
        // Add test metrics
        controller.chain_metrics.insert(8453, ChainMetrics {
            chain_id: 8453,
            opportunity_density: Decimal::new(10, 0), // 10 opportunities
            avg_profit_usd: Decimal::new(50, 0),      // $50 average
            gas_cost_gwei: Decimal::new(20, 0),       // 20 gwei
            success_rate: Decimal::new(8, 1),         // 80% success
            last_updated: SystemTime::now().duration_since(UNIX_EPOCH)
                .unwrap_or_default().as_secs(),
        });
        
        let score = controller.calculate_chain_score(8453).await;
        // Score should be: (10 * 50 * 0.8) - (20 * 0.01) = 400 - 0.2 = 399.8
        assert!(score > Decimal::new(399, 0));
        assert!(score < Decimal::new(400, 0));
    }
}